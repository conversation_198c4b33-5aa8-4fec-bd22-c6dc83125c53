# Metadata | Paystack Developer Documentation

# Metadata

Add custom data to your request payload

## [](#crafting-metadata)Crafting Metadata

With metadata, you can add additional parameters that an endpoint doesn't accept naturally. Crafting metadata will depend on your language's handling of JSON. Common metadata are:

-   Invoice ID
-   Cart ID
-   Cart Items
-   Payment medium (site/app)

There are two ways to add parameters to the metadata object:

1.  **Key/value pair**: You pass the parameter as a key and value pair like this: `cart_id: IU929`. Parameters passed this way don't show up on the dashboard, however, they are returned with the API response.
2.  **Custom Fields**: The `custom_fields` key is reserved for an array of custom fields that should show on the dashboard when you click the transaction.

Custom fields have 3 keys: `display_name`, `variable_name`, and `value`. The display name will be the label for the value when displaying.

-   JSON

1{

2  "metadata": {

3    "cart\_id": 398,

4    "custom\_fields": \[

5      {

6        "display\_name": "Invoice ID",

7        "variable\_name": "Invoice ID",

8        "value": 209

9      },

10      {

11        "display\_name": "Cart Items",

12        "variable\_name": "cart\_items",

13        "value": "3 bananas, 12 mangoes"

14      }

15    \]

16  }

17}

## [](#cancel-action)Cancel Action

You can redirect your users to a chosen URL when they cancel a payment. This is done by setting a `cancel_action` in your metadata:

1"metadata": {

2  "cancel\_action": "https://your-cancel-url.com"

3}

## [](#custom-filters)Custom Filters

Custom filters allow you control how a transaction is completed by using the `custom_filters` object in the `metadata` object.

### [](#recurring-payment)Recurring Payment

If you need to debit your customer in future, specify `recurring=true` in the `custom_filters` object.

1"metadata": {

2  "custom\_filters": {

3    "recurring": true

4  }

5}

This is supported for the Card and Pay with Bank (PwB) channels with a different behaviour for each channel.

#### [](#card)Card

With the `card` channel, we accept only Verve cards that support recurring billing and force a bank authentication for MasterCard and VISA.

#### [](#pay-with-bank)Pay with Bank

With the `pwb` channel, we'll only make the supported banks available for customers to make payment. Banks that don't support recurring payments are filtered out.

### [](#selected-bank-cards)Selected Bank Cards

You can use the `banks` parameter to specify a the bank codes when you only want particular bank cards to be accepted for a transaction. You can use the [List BanksAPI](https://paystack.com/docs/api/miscellaneous#bank) to get the list of supported bank codes.

1"metadata": {

2  "custom\_filters": {

3    "banks": \["057", "100"\]

4  }

5}

### [](#selected-card-brands)Selected Card Brands

If you only want certain card brand(s) to be accepted for a transaction, specify the brands in the `card_brands` array:

1"metadata": {

2  "custom\_filters": {

3    "card\_brands": \["visa"\]

4  }

5}

We currently support the following card brands:

Brand

Code

Country

Verve

`verve`

Nigeria

Visa

`visa`

All regions

Mastercard

`mastercard`

All regions

The filters can also be combined for a comprehensive rule. In the snippet below, the filters tell us that the customer should be enrolled on recurring billing and we should only accept a visa card from **Zenith (057)** or **Suntrust bank (100)**.

-   JSON

1{

2  "metadata": {

3    "custom\_filters": {

4      "recurring": true,

5      "banks": \[

6        "057",

7        "100"

8      \],

9      "card\_brands": \[

10        "visa"

11      \]

12    }

13  }

14}

### [](#selected-bank-accounts)Selected Bank Accounts

The `supported_bank_providers` parameter allows you to specify the banks you want on the [Pay with Bank](/docs/payments/payment-channels/#bank-accounts) channel. When set, the customer will only see the banks you specified. You should use the [List BanksAPI](https://paystack.com/docs/api/miscellaneous#bank) endpoint to get the bank codes.

1"metadata": {

2  "custom\_filters": {

3    "supported\_bank\_providers": \[

4      "033",

5      "215",

6      "102"

7    \]

8  }

9}

### [](#selected-momo-provider)Selected MoMo Provider

Sometimes, you want to give preference to only certain mobile money providers. For example, you might want to run a campaign to allow just a certain provider. To do this, you can specify the provider(s) in the `supported_mobile_money_providers` parameter:

1"metadata": {

2  "custom\_filters": {

3    "supported\_mobile\_money\_providers": \["vod"\]

4  }

5}

Provider

Code

Country

MTN

`mtn`

Ghana

AirtelTigo

`atl`

Ghana

Vodafone

`vod`

Ghana