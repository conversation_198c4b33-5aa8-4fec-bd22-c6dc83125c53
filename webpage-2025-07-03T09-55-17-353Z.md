# Resolve Card BIN | Paystack Developer Documentation

# Resolve Card BIN

This endpoint takes the first 6 digits of a card PAN and returns the following details about the card:

-   Card Type
-   Bin
-   Brand
-   Sub-brand
-   Bank
-   Country code
-   Country name

cURLNodePHP

Show Response

1curl https://api.paystack.co/decision/bin/539983

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-X GET

1{

2  "status": true,

3  "message": "Bin resolved",

4  "data": {

5    "bin": "539983",

6    "brand": "Mastercard",

7    "sub\_brand": "",

8    "country\_code": "NG",

9    "country\_name": "Nigeria",

10    "card\_type": "DEBIT",

11    "bank": "Guaranty Trust Bank",

12    "linked\_bank\_id": 9

13  }

14}

You can use this endpoint to determine the country of issue of a card.

##### Pricing

This endpoint is free.