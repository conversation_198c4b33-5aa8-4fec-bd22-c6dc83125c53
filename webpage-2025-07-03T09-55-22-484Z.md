# Validate Customer | Paystack Developer Documentation

# Validate Customer

Learn how to validate identification details for your customers

##### Availability

This is only required if you're using the [Dedicated Virtual Accounts](/docs/payments/dedicated-virtual-accounts/) feature and your business falls under any of these categories - Betting, Financial services, and General Services.

## [](#introduction)Introduction

The customer validation endpoint is used to verify identification details provided by your customers. You can validate a customer by sending a `POST` request to the [Validate CustomerAPI](https://paystack.com/docs/api/customer#validate) endpoint.

## [](#bank-account-validation)Bank Account Validation

Bank account validation requires that you provide the customer's BVN and a bank account connected to that BVN.

cURLNodePHP

Show Response

1curl https://api.paystack.co/customer/{customer\_code}/identification

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ 

5	"country": "NG",

6	"type": "bank\_account",

7	"account\_number": "**********",

8	"bvn": "************",

9	"bank\_code": "007",

10	"first\_name": "Asta",

11	"last\_name": "Lavista"

12}'

13\-X POST

1{

2  "status": true,

3  "message": "Customer Identification in progress"

4}

### [](#testing)Testing

Customers can only be validated with live keys. However, for testing purposes during your integration, you can make make use of this credential with your test key:

-   Test Credential

1{

2  "country": "NG",

3  "type": "bank\_account",

4  "account\_number": "**********",

5  "bvn": "************",

6  "bank\_code": "007",

7  "first\_name": "Uchenna",

8  "last\_name": "Okoro"

9}

## [](#listen-for-verification-status)Listen for verification status

The verification of the details provided happens asynchronously, and we will send a `customeridentification.success` or `customeridentification.failed` event to your webhook URL when the verification is complete.

##### Prerequisite

You need a basic knowledge of [webhooks](/docs/payments/webhooks/) before proceeding with this section.

-   Customer Identification Success
-   Customer Identification Failed

1{

2  "event": "customeridentification.success",

3  "data": {

4    "customer\_id": "**********",

5    "customer\_code": "CUS\_xnxdt6s1zg1f4nx",

6    "email": "<EMAIL>",

7    "identification": {

8      "country": "NG",

9      "type": "bank\_account",

10      "bvn": "200\*\*\*\*\*677",

11      "account\_number": "012\*\*\*\*789",

12      "bank\_code": "007"

13    }

14  }

15}

When a customer validation fails, we return the cause of the failure in the `data.reason` parameter. We return the following reasons for a failed customer validation:

1.  Account could not be resolved. Please try again
2.  Account name or BVN is incorrect
3.  Account number or BVN is incorrect

##### A validated customer's name cannot be updated

When a customer is validated successfully, the customer's `first_name` and `last_name` are automatically updated to correspond with the name on their BVN. Once this is set, you'll no longer be able to update the customer's name using the [Update CustomerAPI](https://paystack.com/docs/api/customer#update) and the customer's name can only be updated by a re-validation.

## [](#reasons-to-validate-a-customer)Reasons to validate a customer

1.  Local regulations require that customer information is validated before creating account numbers on their behalf.
2.  It allows us name the bank account using the name registered to the provided BVN.