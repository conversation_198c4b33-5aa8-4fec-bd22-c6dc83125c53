# iOS SDK | Paystack Developer Documentation

# iOS SDKBeta

The iOS SDK provides UI components and methods that allow you accept payment in your iOS app.

##### Beta release

The iOS SDK is currently a beta release. If you encounter any issues or have suggestions while using it, [don't hesitate to reach out](https://docs.google.com/forms/d/e/1FAIpQLSf8xRyyAp4_QIBCZisdzoMgoIyIlUuju387O6crTs63scdBag/viewform). We’d love to hear from you!

## [](#introduction)Introduction

The iOS SDK is collection of methods and interfaces that allow developers to build a secure, and convenient payment flow for their iOS applications. Integration is a two-step process:

1.  Initiate the transaction on the server
2.  Complete it on the SDK

## [](#project-requirements)Project Requirements

Paystack iOS SDK is designed to support iOS 13 and above. We do not support OS versions below iOS 13. Before using the SDK, you need to ensure your app meets the following requirements:

-   iOS 13 and above
-   Any OS version on any other OS
-   Swift Package Manager

## [](#installation)Installation

The iOS SDK comes with two packages:

1.  **PaystackCore**: This contains the APIs and methods for processing payments.
2.  **PaystackUI**: This contains the UI components to collect the customer’s payment information and display the payment status.

You can install these packages via the [Swift Package Manager](https://www.swift.org/documentation/package-manager/). To add the required packages, ensure you have the [latest version of XCode](https://developer.apple.com/xcode/) installed and follow these steps:

1.  Select File > Add Package Dependencies…
2.  Copy [the repo URL and paste it](https://github.com/PaystackHQ/paystack-sdk-ios) in the search box of the package dependency

You can read the [Swift Package Manager documentation](https://developer.apple.com/documentation/xcode/adding-package-dependencies-to-your-app) to learn more about adding packages to your project.

## [](#paystack-core)Paystack Core

The `PaystackCore` module uses a builder pattern to handle housekeeping and foundational tasks before the UI components can be used.

-   Swift

1import PaystackCore

The table below shows the methods available in the `Paystack` class:

Methods

Description

`setKey("public-key")`

This method is used to set the public key. Without a public key, you can’t complete a transaction.

`enableLogging()`

This method determines if you get debug logs or not. By default, logging is disabled. Adding this method enables logging.

-   Swift

1let paystack \= try? PaystackBuilder

2        .newInstance

3        .setKey("pk\_domain\_xxxxxxxx")

4        .build()

## [](#initialize-transaction)Initialize Transaction

The SDK requires an `access_code` to display the UI component that accepts payment. To get the `access_code`, make a `POST` request to the [Initialize TransactionAPI](https://paystack.com/docs/api/transaction#initialize) endpoint from your server:

cURLNodePHP

Show Response

1curl https://api.paystack.co/transaction/initialize

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "email": "<EMAIL>", 

5      "amount": "500000"

6    }'

7\-X POST

1{

2  "status": true,

3  "message": "Authorization URL created",

4  "data": {

5    "authorization\_url": "https://checkout.paystack.com/nkdks46nymizns7",

6    "access\_code": "nkdks46nymizns7",

7    "reference": "nms6uvr1pl"

8  }

9}

On a successful initialization of the transaction, you get a response that contains an `access_code`. You need to return this `access_code` back to your mobile app.

##### Secret key safeguarding

Do not make an API request to the Initialize Transaction endpoint directly on your mobile app because it requires your secret key. Your secret key should only be used on your server where stronger security measures can be put in place.

## [](#paystack-ui)Paystack UI

The `PaymentUI` modules holds the UI components that manages the payment form and the available payment channels. You can add this module to your project as shown below:

-   Swift

1import PaystackUI

The `PaystackUI` module comes with two methods:

1.  `chargeUIButton`: This is best suited for SwiftUI.
2.  `presentChargeUI`: This is best suited for UIKit.

### [](#chargeuibutton)chargeUIButton

We provide a button that you can customize to trigger the Payment UI. This method takes the following parameters:

Parameter

Type

Description

`accessCode`

String

This is gotten from your server after initializing a transaction.

`onComplete`

Callback

This method handles all post-payment processes. It’s argument has the type of `TransactionResult`.

-   SwiftUI

1paystack?.chargeUIButton(accessCode: "0peioxfhpn", onComplete: paymentDone) {

2      Text("Initiate Payment")

3}

### [](#presentchargeui)presentChargeUI

With this method, you need to create a button that would trigger the display of the Payment UI. The Payment UI requires a view to hook to, so you need to provide a reference to the view controller of the view being displayed, alongside other parameters listed in the table below:

Parameter

Type

Description

`on`

ViewController

The view controller of the current view.

`accessCode`

String

This is gotten from your server after initializing a transaction.

`onComplete`

Callback

This method handles all post-payment processes. It’s argument has the type of `TransactionResult`.

-   UIKit

1@IBAction func payButtonTapped(\_ sender: Any) {

2      paystack?.presentChargeUI(on: self,

3                              accessCode: "0peioxfhpn",

4                              onComplete: paymentDone)

5}

## [](#transaction-result)Transaction Result

The `TransactionResult` exposes the different states of a transaction. It is the argument type for the `PaystackUI` `onComplete` callback:

State

Description

Completed

The customer completed the payment process. We return a `paymentCompletionDetails` that contains the transaction reference. You should use the transaction reference to verify the transaction status and amount on the server before providing value.

Cancelled

The customer cancelled the payment.

Failed

An error occurred during the payment process. This result contains a `ChargeError`. The `ChargeError` contains a `cause` property which details the actual error received and should be used for error handling and logging

-   Swift

1func paymentDone(\_ result: TransactionResult) {

2  switch (result){

3    case .completed(let details):

4      print("Transaction completed with reference: (details.reference)")

5    case .cancelled:

6      print("Transaction was cancelled")

7    case .error(error: let error, reference: let reference):

8      print("An error occured: (error.message) with reference: (String(describing: reference))")

9  }

10}