# Apple Pay | Paystack Developer Documentation

# Apple Pay

In A Nutshell

##### In a nutshell

Allow customers to securely make payments using Apple Pay on iOS and Safari.

## [](#activating-apple-pay)Activating Apple Pay

##### Supported devices

Apple Pay only works on [Apple devices and the Safari browser](https://developer.apple.com/documentation/apple_pay_on_the_web/).

You can configure the Apple Pay channel for your web application on the Paystack Dashboard. To enable Apple Pay on your integration, [go to the Preferences page](https://dashboard.paystack.com/#/settings/preferences) on the Paystack dashboard and check the `Apple Pay` option.

[![Image of the Paystack preferences page showing the checkbox to activate Apple Pay](/docs/static/********************************/8c557/activate_apple_pay.png)](/docs/static/********************************/636d3/activate_apple_pay.png)

Once checked, you'll be prompted to enable Apple Pay by accepting the Apple Pay Platform Web Merchant Terms and Conditions. Clicking on the `Enable Apple Pay` button means you've read and accepted the Apple Pay Terms and Conditions.

[![Image of modal to review Apple Pay terms and conditions before activating Apple Pay](/docs/static/370d4300cad1b4debc0f449e5a57e94f/8c557/review_tc.png)](/docs/static/370d4300cad1b4debc0f449e5a57e94f/e431d/review_tc.png)

##### Commerce Suite and Hosted Checkout

If you make use of our Commerce Suite (Invoices, Payment Pages or Storefronts) or any of our plugins that redirects the user away from your website to a new page for payment, you can now start accepting payments through Apple Pay. Merchants with a custom web application can proceed to the next section to set up Apple Pay for their website.

## [](#setting-up-for-your-web-application)Setting up for your web application

Every Apple Pay request needs to be associated with a top-level domain or subdomain. To begin accepting payment with Apple Pay, you need to:

1.  [Register your domain](/docs/payments/apple-pay/#register-your-domain)
2.  [Host your domain verification file](/docs/payments/apple-pay/#host-your-domain-verification-file)
3.  [Verify your domain](/docs/payments/apple-pay/#verify-your-domain)

### [](#register-your-domain)Register your domain

On the Paystack Dashboard:

1.  Click on the `Apple Pay` tab on the Settings page
2.  Under the Web Domains section, click on `Add new domain` button [![Image of the Paystack Apple Pay page, showinfg where to add a domain name](/docs/static/45a79b8fc5a8474c8c71ba04b18d622a/8c557/add_domain.png)](/docs/static/45a79b8fc5a8474c8c71ba04b18d622a/a1ccc/add_domain.png)
    
3.  On the modal, add your domain name and download the domain verification file. The verification file will be required in the next step. [![Image of the domain verification modal showing a domain name being registered](/docs/static/4117498d4464cec3d1294002728a6363/8c557/filled_domain.png)](/docs/static/4117498d4464cec3d1294002728a6363/69d6b/filled_domain.png)
    

##### Domain Registration via API

If you would rather register your domain via API, kindly check the [Apple PayAPI](https://paystack.com/docs/api/apple-pay) endpoint for more details.

### [](#host-your-domain-verification-file)Host your domain verification file

For each top-level domain and/or subdomain registered, you need to host a verification file in the `.well-known` folder in your website's root directory.

For example, if you registered `paystack.com`, then the verification file should reside in: `https://paystack.com/.well-known`. Likewise, if you registered `music.paystack.com`, the verification file should reside in: `https://music.paystack.com/.well-known`.

Hosting the verification file is a two-step process:

1.  Create a `.well-known` folder in your website's root directory
2.  Add the verification file previously downloaded to this folder

[![Image of an application folder showing the well known folder used to host the domain verification file](/docs/static/d4551ff8fd571a49e50162ca7307938b/8c557/create_well_known.png)](/docs/static/d4551ff8fd571a49e50162ca7307938b/72aae/create_well_known.png)

##### Domain Verification File Content-Type

Kindly ensure the domain verification file is returned with a `content-type` of `application/text`. Using a different `content-type` could cause your customer's payment to fail.

### [](#verify-your-domain)Verify your domain

You should now go back to the dashboard to verify your domain. On the `Verify your domain` modal, click the `Verify Domain` button to complete your domain registration. If all goes well, you should get a domain verified message like this:

[![Image of a successful domain verification for Apple Pay](/docs/static/592c1e7cb01f3947c4256069bf1e01e3/8c557/domain_verified.png)](/docs/static/592c1e7cb01f3947c4256069bf1e01e3/21335/domain_verified.png)

## [](#setting-up-on-popup)Setting up on Popup

##### Compatibility

Apple Pay works on version 2 of our InlineJS only. If you are using version 1 [follow this migration guide](/docs/guides/migrating-from-inlinejs-v1-to-v2/) before proceeding.

There are two ways to set up Apple Pay in your frontend application:

1.  Mounting a payment request button
2.  Using a pre-checkout modal

### [](#mounting-a-payment-request-button)Mounting a payment request button

This method allows you to position the Apple Pay button in line with your design. It's a flexible method with a two-step process:

1.  Create a container
2.  Set up the payment request

#### [](#create-a-container)Create a container

This process involves adding the HTML tags that will house the Apple Pay button as well as the button to load other payment options:

-   HTML

1<!-- Payment request buttons, styles and event listeners will be injected to this div -->

2<div id\="paystack-apple-pay"\></div\>

3<button id\="paystack-other-channels"\>More payment options</button\>

You should use a `div` tag with a unique `id` for the container of the Apple Pay button. `paystack-apple-pay` is used in the snippet above, however, you can use any name you prefer. This `id` will be used to configure and load the Apple Pay button.

#### [](#set-up-the-payment-request)Set up the payment request

In order to configure the Apple Pay button, you need to pass the container `id` and the `id` for the other channels in the `paymentRequest()` method:

-   Javascript

1const paystackPop \= new PaystackPop();

2

3await paystackPop.paymentRequest({

4  key: 'pk\_domain\_xxxxx', // Replace with your public key

5  email: 'CUSTOMER\_EMAIL',

6  amount: amount \* 100, // the amount value is multiplied by 100 to convert to the lowest currency unit

7  currency: 'NGN', // Use NGN for Naira or USD for US Dollars

8  ref: 'YOUR\_REFERENCE', // Replace with a reference you generated

9  container: 'paystack-apple-pay', // ID of div to mount payment button elements

10  loadPaystackCheckButton: 'paystack-other-channels', // ID of button to trigger opening Paystack checkout (optional)

11  style: { 

12    theme: 'dark', // 'light' or 'dark'

13    applePay: { 

14      margin: '10px',

15      padding: '10px',

16      width: '100%',

17      borderRadius: '5px',

18      type: 'pay', // Default value is 'pay'. See other apple pay button types here https://developer.apple.com/documentation/apple\_pay\_on\_the\_web/displaying\_apple\_pay\_buttons\_using\_css

19      locale: 'en'

20    } 

21  }, // custom styles for button elements

22  onSuccess(response) {

23    // handle success

24  },

25  onError() {

26    // handle error

27  },

28  onCancel() {

29    // handle cancel

30  },

31  onElementsMount(elements) { // { applePay: true } or null

32    

33  }

34});

### [](#using-a-pre-checkout-modal)Using a pre-checkout modal

This method doesn't require any additional HTML elements as we do the heavy lifting for you. You can simply tie your payment button to the `checkout()` method:

-   Javascript

1const paystackPop \= new PaystackPop();

2

3async function payWithPaystack() {

4  await paystack.checkout({

5    key: 'pk\_domain\_xxxxx',

6    email: '<EMAIL>',

7    amount: 10000,

8    onSuccess: (transaction) \=> { 

9      console.log("Transaction: ", transaction)

10    },

11    onCancel: () \=> {

12      console.log("Pop up closed!")

13    }

14  });

15}

The checkout method checks if the user is on an iOS device or the Safari browser. If the user is on an Apple device, we load a pre-checkout modal that contains the Apple Pay button and a button to load other payment options. Otherwise, we simply load the checkout:

[![Image of a pre-checkout modal on an iPhone](/docs/static/04dfa1adaa240a382e655b313caad56a/8c557/pre_checkout.png)](/docs/static/04dfa1adaa240a382e655b313caad56a/25c1c/pre_checkout.png)

## [](#troubleshooting)Troubleshooting

If you experience any issues with your Apple Pay integration, kindly ensure there wasn't an oversight during set up:

-   Your server must serve all requests via HTTPS
-   You've [registered and verified your domain](/docs/payments/apple-pay/#register-your-domain) on the dashboard
-   You've hosted your domain verification file in the `.well-known` folder in your website's root directory
-   If your server requires an external server whitelisted before communication can be initiated, you will need to [whitelist Apple's IP addresses](https://developer.apple.com/documentation/apple_pay_on_the_web/setting_up_your_server).

If all checks out and you are still having issues with your Apple Pay integration, here are some possible errors and resolution you can try out:

Error

Resolution

Apple Pay Session could not be initiated

We couldn’t initiate a web session for the payment. Kindly try again.

Apple Pay Transaction not allowed because merchant is not enabled for international payments.

You need to check if your [business is eligible for international payment](https://support.paystack.com/hc/en-us/articles/************-Is-my-business-eligible-for-international-payments-). If your business is eligible, kindly [follow this guide to activate international payment](https://support.paystack.com/hc/en-us/articles/************-How-do-I-turn-on-international-payments-on-Paystack-).

Merchant's Category not supported for Apple Pay transactions.

For compliance reasons, only specific business categories are allowed to use Apple Pay.