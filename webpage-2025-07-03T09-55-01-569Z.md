# Flutter SDK | Paystack Developer Documentation

# Flutter SDKBeta

In A Nutshell

##### In a nutshell

The Flutter SDK provides methods and models that allow you to accept payment in your Flutter app.

##### Beta Release

The Flutter SDK is currently a beta release. If you encounter any issues or have suggestions while using it, you can [create an issue](https://github.com/PaystackOSS/paystack_flutter/issues) to let us know about it or [open a PR](https://github.com/PaystackOSS/paystack_flutter/pulls) to add a feature.

## [](#introduction)Introduction

Paystack Flutter SDK provides methods that allow developers to build a secure, and convenient payment flow for their Flutter applications. Integration is a two-step process:

1.  Initiate the transaction on the server
2.  Complete it on the SDK

The benefit of this flow is that you keep your secret key away from the client side, which is a secure standard practice.

## [](#project-requirements)Project Requirements

Paystack Flutter SDK adopts recent patterns in Android and iOS which limits the older versions of these OSs that we can support. When building with the Flutter SDK, ensure your project meets these requirements:

-   Flutter >= 3.0.0
-   iOS >= 13
-   Android: Min SDK - 23 and Compile SDK - 34

##### Android activity requirement

At the moment, Flutter 3.3.0 below extends the `FlutterActivity` as the base class for Android. The `FlutterActivity` doesn't have the `ComponentActivity`, a compulsory necessity for loading the payment views with the SDK, in its ancestral tree. To fix this, change the `FlutterActivity` to `FlutterFragmentActivity` in the `MainActivity` in the **android** folder of your project.

## [](#getting-started)Getting Started

To add the [Paystack Flutter SDK](https://pub.dev/packages/paystack_flutter_sdk) to your project, run the command below in your terminal:

-   Terminal

1flutter pub add paystack\_flutter\_sdk

This command adds the `paystack_flutter_sdk` to your package’s dependencies in the `pubspec.yaml` file and installs it for usage in your project. To use the library, import it in your `.dart` file as shown below:

-   Dart

1import 'package:paystack\_flutter\_sdk/paystack\_flutter\_sdk.dart';

## [](#initialize-transaction)Initialize Transaction

The SDK requires an `access_code` to display the UI component that accepts payment. To get the `access_code`, make a `POST` request to the [Initialize TransactionAPI](https://paystack.com/docs/api/transaction#initialize) endpoint from your server:

cURLNodePHP

Show Response

1curl https://api.paystack.co/transaction/initialize

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "email": "<EMAIL>", 

5      "amount": "500000"

6    }'

7\-X POST

1{

2  "status": true,

3  "message": "Authorization URL created",

4  "data": {

5    "authorization\_url": "https://checkout.paystack.com/nkdks46nymizns7",

6    "access\_code": "nkdks46nymizns7",

7    "reference": "nms6uvr1pl"

8  }

9}

On a successful initialization of the transaction, you get a response that contains an `access_code`. You need to return this `access_code` back to your mobile app.

##### Secret key safeguarding

Do not make an API request to the Initialize Transaction endpoint directly on your mobile app because it requires your secret key. Your secret key should only be used on your server where stronger security measures can be put in place.

## [](#paystack-class)Paystack Class

The `Paystack` class provides methods for managing payments in your app. With the SDK imported into your project, you need to initialize this class to access its methods:

-   Initialization

1final \_paystack \= Paystack();

The Paystack class comes with the following methods, available via its instance:

-   initialize()
-   launch()

### [](#initialize)initialize()

This method allows you set the configuration for how the SDK operates. The method signature is:

-   Method Signature

1Future<bool\> initialize(String publicKey, bool enableLogging)

It is an asynchronous method that returns a boolean value and accepts the following parameters:

Parameter

Type

Required

Description

`publicKey`

String

Yes

This is the public key gotten from Paystack Dashboard. It should be the one tied to the secret key used to initialize the transaction.

`enableLogging`

Boolean

No

This is used to indicate if you’d like to get the logs of the Android and iOS platforms.

Here’s an example of using the `initialize` method:

-   Method usage

1String publicKey \= "pk\_domain\_xxxxxx";

2

3try {

4  final response \= await \_paystack.initialize(publicKey, true); // allow logging

5  if (response) {

6    log("Sucessfully initialised the SDK");

7  } else {

8     log("Unable to initialise the SDK");

9  }

10} on PlatformException catch (e) {

11   log(e.message!);

12}

### [](#launch)launch()

This method is used for completing the transaction that you previously initialized on your server. It loads the payment UI which your customers interact with when completing their payment. The method signature is:

-   Method Signature

1Future<TransactionResponse\> launch(String accessCode)

It is an asynchronous method that returns a `TransactionResponse` and accepts the following parameter:

Parameter

Type

Required

Description

`accessCode`

String

Yes

This is the `access_code` in the response from the transaction initialization request.

Here’s an example of using the `launch` method:

-   Method Signature

1String reference \= "";

2

3try {

4  final response \= await \_paystack.launch(\_accessCode);

5  if (response.status \== "success") {

6    reference \= response.reference;

7    log(reference);

8  } else if(response.status \== "cancelled") {

9    log(response.message);

10  } else {

11    log(response.message);

12  }

13} on PlatformException catch (e) {

14  log(e.message!);

15}

When your integration is done right, you would need to handle the `TransactionResponse` model. However, there are cases where you could experience some integration bugs. Exceptions are used for handling integration errors in the `Paystack` class methods, all consolidated into the `PaystackException` model. The sections below describe how to handle both the `TransactionResponse` and `PaystackException`.

## [](#transactionresponse)TransactionResponse

The `TransactionResponse` model is returned when you've successfully integrated the SDK and able to get the payment UI. It is tied to the transaction lifecycle and has the following parameters:

Parameter

Type

Description

`status`

String

Indicates the state of the transaction. Possible values are: `success` | `cancelled` | `failed`.

`message`

String

This is a short detail about the status of the transaction.

`reference`

String

This is a unique identifier used to manage post-payment processes. It is only returned when the `status` is `success`.

## [](#paystackexception)PaystackException

Exceptions are thrown during your integration process. There are mostly issues surrounding the development process. `PaystackException` makes use of the platform-specific `FlutterError` model which has the following parameters:

Parameter

Type

Description

`code`

String

This indicates the category of error.

`message`

String

This is a short description of the error.

`details`

String

This currently returns `nil`. In a future update, we can use it to provide details about fixing the error.

This means you can parse any exception as shown below:

-   Exception handling

1try {

2  final response \= await \_paystack.launch(\_accessCode);

3  // rest of code

4} on PlatformException catch (e) {

5  log(e.code!);

6  log(e.message!);

7}

The following are the error codes that the `PaystackException` returns:

Error code

Description

`INVALID_ARGUMENT`

This occurs when you aren't passing the required parameter(s) to set up the SDK.

`INITIALIZATION_ERROR`

This occurs when the SDK cannot be initialized with the parameters passed.

`UNSUPPORTED_VERSION`

This occurs when your projects doesn't conform to the SDK requirements.

`MISSING_VIEW`

This occurs when the SDK cannot find a view controller (iOS) or Activity (Android) to attach to.

`LAUNCH_ERROR`

This occurs when the payment UI cannot be loaded.