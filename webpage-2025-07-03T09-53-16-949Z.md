# Multi-split Payments | Paystack Developer Documentation

# Multi-split Payments

In A Nutshell

##### In a nutshell

Multi-split enables merchants to split the settlement for a transaction across their payout account, and one or more subaccounts.

You can implement multi-split by:

1.  [Creating a transaction split](/docs/payments/multi-split-payments/#creating-a-transaction-split)
2.  [Using transaction splits with payments](/docs/payments/multi-split-payments/#using-transaction-splits-with-payments)

Merchants will be able to create and manage their transaction splits using the [Transaction SplitsAPI](https://paystack.com/docs/api/split) endpoints and their [Paystack Dashboard](https://dashboard.paystack.com/#/transaction-splits).

## [](#creating-a-transaction-split)Creating a transaction split

##### Before you begin

Transaction split depends on subaccounts to work its magic. If you already have subaccounts, you can proceed. Otherwise, you'll need to [create a subaccount](/docs/payments/split-payments/#create-a-subaccount) first.

You can create a transaction split by sending a request to the [Create SplitAPI](https://paystack.com/docs/api/split#create) endpoint with the following information:

-   The type of split (`flat` or `percentage`)
-   An array of subaccounts and their respective shares

cURLNodePHP

Show Response

1#!/bin/sh

2url\="https://api.paystack.co/split"

3authorization\="Authorization: Bearer YOUR\_SECRET\_KEY"

4content\_type\="Content-Type: application/json"

5data\='{ 

6  "name":"Halfsies", 

7  "type":"percentage", 

8  "currency": "NGN",

9  "subaccounts":\[{

10    "subaccount": "ACCT\_6uujpqtzmnufzkw",

11    "share": 50

12  }\]

13}'

14

15curl "$url" \-H "$authorization" \-H "$content\_type" \-d "$data" \-X POST

1{

2  "status": true,

3  "message": "Split created",

4  "data": {

5    "id": 2703655,

6    "name": "Halfsies",

7    "type": "percentage",

8    "currency": "NGN",

9    "integration": 463433,

10    "domain": "test",

11    "split\_code": "SPL\_RcScyW5jp2",

12    "active": true,

13    "bearer\_type": "all",

14    "createdAt": "2024-08-26T11:38:47.506Z",

15    "updatedAt": "2024-08-26T11:38:47.506Z",

16    "is\_dynamic": false,

17    "subaccounts": \[

18      {

19        "subaccount": {

20          "id": 1151727,

21          "subaccount\_code": "ACCT\_6uujpqtzmnufzkw",

22          "business\_name": "Oasis Global",

23          "description": "Oasis Global",

24          "primary\_contact\_name": null,

25          "primary\_contact\_email": null,

26          "primary\_contact\_phone": null,

27          "metadata": null,

28          "settlement\_bank": "Guaranty Trust Bank",

29          "currency": "NGN",

30          "account\_number": "**********"

31        },

32        "share": 50

33      }

34    \],

35    "total\_subaccounts": 1

36  }

37}

##### Warning

1.  We don't accept decimal figures for subaccounts shares if the split type is `percentage`
2.  The sum of the `percentage` splits should not be more than 100%
3.  Share amounts for `flat` splits should be in the subunit of the [supported currency](/api#supported-currency)
4.  The sum of the `flat` splits should not be more than the transaction amount
5.  A split can either be a `flat` or `percentage` type, not both

Through the APIs, you can add, remove, and update the subaccounts in a Split, as well as change the active state of a Split.

##### Updating a Split

You can't change the type of a split. Instead, you can deactivate the Split and create a new Split with a different split type.

## [](#using-transaction-splits-with-payments)Using transaction splits with payments

After creating a transaction split, you can make use of it when:

1.  [Initializing a transaction](/docs/payments/multi-split-payments/#initializing-a-transaction)
2.  [Charging an authorization](/docs/payments/multi-split-payments/#charging-an-authorization)

### [](#initializing-a-transaction)Initializing a transaction

You can initialize a transaction with a Transaction Split by adding a `split_code` to the payload you send to the [Transaction InitializeAPI](https://paystack.com/docs/api/transaction#initialize) endpoint.

cURLNodePHP

Show Response

1curl https://api.paystack.co/transaction/initialize

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "email": "<EMAIL>", 

5      "amount": "20000", 

6      "split\_code": "SPL\_98WF13Eekw" 

7    }'

8\-X POST

1{

2  "status": true,

3  "message": "Authorization URL created",

4  "data": {

5    "authorization\_url": "https://checkout.paystack.com/nkdks46nymizns7",

6    "access\_code": "nkdks46nymizns7",

7    "reference": "nms6uvr1pl"

8  }

9}

### [](#charging-an-authorization)Charging an authorization

To charge an authorization with a Transaction Split, add a `split_code` to the payload you send to the [Charge AuthorizationAPI](https://paystack.com/docs/api/transaction#charge-authorization) endpoint.

cURLNodePHP

Show Response

1curl https://api.paystack.co/transaction/charge\_authorization

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "authorization\_code" : "AUTH\_12abc345de", "email": "<EMAIL>", 

5      "amount": "300000", "split\_code": "SPL\_UO2vBzEqHW" }'

6\-X POST

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "amount": 300000,

6    "currency": "NGN",

7    "transaction\_date": "2020-05-27T11:45:03.000Z",

8    "status": "success",

9    "reference": "cn65lf4ixmkzvda",

10    "domain": "test",

11    "metadata": "",

12    "gateway\_response": "Approved",

13    "message": null,

14    "channel": "card",

15    "ip\_address": null,

16    "log": null,

17    "fees": 14500,

18    "authorization": {

19      "authorization\_code": "AUTH\_12abc345de",

20      "bin": "408408",

21      "last4": "4081",

22      "exp\_month": "12",

23      "exp\_year": "2020",

24      "channel": "card",

25      "card\_type": "visa DEBIT",

26      "bank": "Test Bank",

27      "country\_code": "NG",

28      "brand": "visa",

29      "reusable": true,

30      "signature": "SIG\_2Gvc6pNuzJmj4TCchXfp",

31      "account\_name": null

32    },

33    "customer": {

34      "id": ********,

35      "first\_name": null,

36      "last\_name": null,

37      "email": "<EMAIL>",

38      "customer\_code": "CUS\_wt0zmhzb0xqd4nr",

39      "phone": null,

40      "metadata": null,

41      "risk\_action": "default"

42    },

43    "plan": null,

44    "id": *********

45  }

46}

## [](#dynamic-splits)Dynamic Splits

Sometimes, you can't determine a split configuration until later in the purchase flow. With dynamic splits, you can create splits on the fly. This can be achieved by passing a `split` object to the [Transaction InitializeAPI](https://paystack.com/docs/api/transaction#initialize) endpoint or to [popup](/docs/payments/accept-payments/#popup). The `split` object can take the following properties:

Param

Required?

Description

`type`

Yes

Value can be `flat` or `percentage`

`bearer_type`

Yes

Value can be `all`, `all-proportional`, `account` or `subaccount`

`subaccounts`

Yes

An array of subaccount object. e.g. `{"subaccount": 'ACCT_', "share": 60}`

`bearer_subaccount`

No

Subaccount code of the bearer. It should be specified if `bearer_type` is `subaccount`

`reference`

No

Unique reference of the split

cURLNodePHP

Show Response

1curl https://api.paystack.co/transaction/initialize

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "email": "<EMAIL>", 

5      "amount": "20000", 

6      "split": {

7        "type": "flat",

8        "bearer\_type": "account",

9        "subaccounts": \[

10          {

11            "subaccount": "ACCT\_pwwualwty4nhq9d",

12            "share": 6000

13          },

14          {

15            "subaccount": "ACCT\_hdl8abxl8drhrl3",

16            "share": 4000

17          },

18        \]

19      } 

20}'

21\-X POST

1{

2  "status": true,

3  "message": "Authorization URL created",

4  "data": {

5    "authorization\_url": "https://checkout.paystack.com/nkdks46nymizns7",

6    "access\_code": "nkdks46nymizns7",

7    "reference": "nms6uvr1pl"

8  }

9}

## [](#webhooks)Webhooks

For successful transactions, we'll populate a `split` parameter in the `charge.success` webhook with information on the Split:

-   JSON

1{

2  "event": "charge.success",

3  "data": {

4    "id": *********,

5    "domain": "live",

6    "status": "success",

7    "reference": "12k81tq3my",

8    "amount": 10000,

9    "message": null,

10    "gateway\_response": "Approved",

11    "paid\_at": "2020-05-28T13:54:57.000Z",

12    "split": {

13      "id": 10,

14      "name": "Example Split",

15      "split\_code": "SPL\_98WF13Eekw",

16      "formula": {

17        "type": "percentage",

18        "subaccounts": \[

19          {

20            "share": 20,

21            "subaccount\_code": "ACCT\_zpu16k4uhxycmxu",

22            "id": 12850,

23            "name": "Ayobami UBA"

24          }

25        \],

26        "integration": 80

27      },

28      "shares": {

29        "paystack": 140,

30        "subaccounts": \[

31          {

32            "amount": 2000,

33            "subaccount\_code": "ACCT\_zpu16k4uhxycmxu",

34            "id": 12850

35          }

36        \],

37        "integration": 7860

38      }

39    }

40  }

41}

## [](#fees-on-multi-split)Fees on Multi-split

By default, the main account bears the Paystack transaction fees. However, you can change this by setting the `bearer_type` to either `account`, `all` or `subaccount`.

Bearer Type

Description

`all`

Fees will be shared equally by the main account and all subaccounts.

`all-proportional`

Fees will be charged according to the share of the main account and subaccounts.

`account`

Fees will be charged to the main account only.

`subaccount`

Only the subaccount set in the `bearer_subaccount` will be charged

##### Bearer Subaccount

A required parameter when the `bearer_type` is set to `subaccount`. The value of `bearer_subaccount` must be a subaccount code that's in the split group.