# Terminal | Paystack Developer Documentation

# Terminal

Learn how to build delightful in-person payment experiences with Paystack Terminal

## [](#getting-started)Getting started

Paystack Terminal enables a developer to build custom applications that create unique branded in-person payment experiences. We provide the following integration flows that cater to different use cases:

[

#### Invoice Payments

Allow customers make payment for items on order fulfillment



](/docs/terminal/invoice-payments/)[

#### Push Payment Requests

Accept in-person payment on your point of sales system



](/docs/terminal/push-payment-requests/)[

#### Custom apps

Build apps on the Terminal by leveraging our payment intents



](/docs/terminal/custom-apps/)[

#### Virtual Terminal

Accept in-person payments without a POS device



](/docs/terminal/virtual-terminal/)

## [](#explore-demos)Explore demos

We’ve put together simple projects to demonstrate how the Terminal works for various use cases. [Explore all demos](https://github.com/PaystackOSS/) or start with the most popular ones below.

##### Invoice Payments

[PaystackOSS/sample-logistics](https://github.com/PaystackOSS/sample-logistics)

APIS USED

---

-   [Create Customer](/docs/payments/accept-payments/)
-   [Payment Request](/docs/payments/verify-payments/)

Vue

##### Push Payment Requests

[PaystackOSS/sample-restaurant](https://github.com/PaystackOSS/sample-restaurant)

APIS USED

---

-   [Payment Request](/docs/payments/accept-payments/)
-   [Terminal Event](/docs/payments/verify-payments/)

React

##### Custom App

[PaystackOSS/sample-registration-react-native](https://github.com/PaystackOSS/sample-registration-react-native)

APIS USED

---

-   [Terminal Intent](/docs/terminal/custom-apps/#building-an-intent)

React Native

##### Custom App

[PaystackOSS/sample-registration-flutter](https://github.com/PaystackOSS/sample-registration-flutter)

APIS USED

---

-   [Terminal Intent](/docs/terminal/custom-apps/#building-an-intent)

Flutter