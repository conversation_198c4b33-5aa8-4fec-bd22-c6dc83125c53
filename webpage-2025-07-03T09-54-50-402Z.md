# Android SDK | Paystack Developer Documentation

# Android SDKBeta

The Android SDK provides UI components and methods that allow you accept payment in your Android app.

##### Beta Release

The Android SDK is currently a beta release. If you encounter any issues or have suggestions while using it, [don't hesitate to reach out](https://docs.google.com/forms/d/e/1FAIpQLSf8xRyyAp4_QIBCZisdzoMgoIyIlUuju387O6crTs63scdBag/viewform). We’d love to hear from you!

## [](#introduction)Introduction

The Android SDK is collection of methods and interfaces that allow developers to build a secure, and convenient payment flow for their Android applications. Integration is a two-step process:

1.  Initiate the transaction on the server
2.  Complete it on the SDK

## [](#project-requirements)Project Requirements

Paystack Android SDK is designed to support [Android 6.0 (API level 23)](https://developer.android.com/tools/releases/platforms#6.0) and above. We do not support OS versions below Android 6.0. Before using the SDK, you need to ensure your app meets the following requirements:

-   Android 6.0 (API level 23) and above
-   Android Gradle Plugin 7.2 and above
-   Gradle 7.1.3 and above
-   AndroidX

## [](#installation)Installation

To use the SDK, add the `paystack-ui` in the `dependencies` block of your app-level build file:

##### Latest dependency version

You should check [Maven Central](https://central.sonatype.com/artifact/com.paystack.android/paystack-ui/versions) to get the latest version before installation.

-   build.gradle

1dependencies {

2  implementation 'com.paystack.android:paystack-ui:0.0.9'

3}

Ensure you sync the project to download the SDK into your project. On successful installation, you’d have access to the UI components and methods to accept payment in your Android app.

## [](#paystack-builder)Paystack Builder

The `Paystack` class uses a builder pattern to handle housekeeping and foundational tasks before the UI components can be used. It is part of the core library:

-   Kotlin
-   Java

1import com.paystack.android.core.Paystack

The table below shows the methods available in the `Paystack` class:

Methods

Description

`setPublicKey("public-key")`

This method is used to set the public key. Without a public key, you can’t complete a transaction.

`setLoggingEnabled(boolean)`

This method determines if you get debug logs or not. By default, it’s set to `false` which means you won’t get any debug log.

-   Kotlin
-   Java

1Paystack.builder()

2    .setPublicKey("pk\_domain\_xxxxxxxx")

3    .setLoggingEnabled(true)

4    .build()

## [](#initialize-transaction)Initialize Transaction

The SDK requires an `access_code` to display the UI component that accepts payment. To get the `access_code`, make a `POST` request to the [Initialize TransactionAPI](https://paystack.com/docs/api/transaction#initialize) endpoint from your server:

cURLNodePHP

Show Response

1curl https://api.paystack.co/transaction/initialize

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "email": "<EMAIL>", 

5      "amount": "500000"

6    }'

7\-X POST

1{

2  "status": true,

3  "message": "Authorization URL created",

4  "data": {

5    "authorization\_url": "https://checkout.paystack.com/nkdks46nymizns7",

6    "access\_code": "nkdks46nymizns7",

7    "reference": "nms6uvr1pl"

8  }

9}

On a successful initialization of the transaction, you get a response that contains an `access_code`. You need to return this `access_code` back to your mobile app.

##### Secret key safeguarding

Do not make an API request to the Initialize Transaction endpoint directly on your mobile app because it requires your secret key. Your secret key should only be used on your server where stronger security measures can be put in place.

## [](#payment-sheet)Payment Sheet

The `PaymentSheet` is the UI component that contains the payment form and the available payment channels. It is part of the UI library that can be imported as shown below:

-   Kotlin
-   Java

1import com.paystack.android.ui.paymentsheet.PaymentSheet

The `PaymentSheet` class is initialized in the `onCreate` method of your `Activity` or `Fragment` with two arguments:

Arguments

Example

Description

Activity

`this`

This is the reference to your Activity or Fragment

Callback

`paymentResult`

This is the method that handles all post-payment processes. It’s argument has the type of `PaystackSheetResult`

-   Kotlin
-   Java

1private lateinit var paymentSheet: PaymentSheet

2

3override fun onCreate(savedInstanceState: Bundle?) {

4  super.onCreate(savedInstanceState)

5  setContentView(R.layout.activity\_main)

6

7  // library initialization code snippets and others go here

8

9  paymentSheet \= PaymentSheet(this, ::paymentComplete)

10

11  // more snippet

12}

The `PaymentSheet` comes with a `launch` method that allows you trigger the display of the component. The launch method takes the `access_code` from a previously initialized transaction.

-   Kotlin
-   Java

1fun makePayment() {

2  // Pass access\_code from transaction initialize call on the server

3  paymentSheet.launch("br6cgmvflhn3qtd")

4}

## [](#payment-sheet-result)Payment Sheet Result

The `PaymentSheetResult` exposes the different states of a transaction. It is the argument type for the `PaymentSheet` callback:

-   Kotlin
-   Java

1import com.paystack.android.ui.paymentsheet.PaymentSheetResult

2

3private fun paymentComplete(paymentSheetResult: PaymentSheetResult) {

4    

5}

The different state that the `PaymentSheetResult` exposes are:

State

Description

Completed

The customer completed the payment process. We return a `paymentCompletionDetails` that contains the transaction reference. You should use the transaction reference to verify the transaction status and amount on the server before providing value.

Cancelled

The customer cancelled the payment.

Failed

An error occurred during the payment process. This result contains an `error`. It may also contain a `reference` for the transaction.

-   Kotlin
-   Java

1private fun paymentComplete(paymentSheetResult: PaymentSheetResult) {

2  val message \= when (paymentSheetResult) {

3    PaymentSheetResult.Cancelled \-> "Cancelled"

4    is PaymentSheetResult.Failed \-> {

5      Log.e("Something went wrong", paymentSheetResult.error.message.orEmpty(), paymentSheetResult.error)

6      paymentSheetResult.error.message ?: "Failed"

7    }

8

9    is PaymentSheetResult.Completed \-> {

10      // Returns the transaction reference  PaymentCompletionDetails(reference={TransactionRef})

11      Log.d("Payment successful", paymentSheetResult.paymentCompletionDetails.toString())

12      "Successful"

13    }

14  }

15

16  Toast.makeText(this, "Payment $message", Toast.LENGTH\_SHORT).show()

17}