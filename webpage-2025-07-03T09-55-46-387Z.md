# Single Transfers | Paystack Developer Documentation

# Single Transfers

In A Nutshell

##### In a nutshell

Send money to your customers by using the Transfer API

You can transfer money in four easy steps:

1.  [Create a transfer recipient](/docs/transfers/single-transfers/#create-a-transfer-recipient)
2.  [Generate a transfer reference](/docs/transfers/single-transfers/#generate-a-transfer-reference)
3.  [Initiate a transfer](/docs/transfers/single-transfers/#initiate-a-transfer)
4.  [Listen for transfer status](/docs/transfers/single-transfers/#listen-for-transfer-status)

##### Before you begin!

To send money on Paystack, you need API keys to authenticate your transfers. You can find your keys on the Paystack Dashboard under [Settings → API Keys & Webhooks](https://dashboard.paystack.com/#/settings/developer).

## [](#create-a-transfer-recipient)Create a transfer recipient

A transfer recipient is a beneficiary on your integration that you can send money to. Before sending money to your customer, you need to collect their details first, then use their details to [create a transfer recipient](/docs/transfers/creating-transfer-recipients/). We support different recipients in different countries:

Type

Description

Currency

`ghipss`

This means Ghana Interbank Payment and Settlement Systems. It represents bank account in Ghana.

**GHS**

`mobile_money`

Mobile Money or MoMo is an account tied to a mobile number.

**GHS/KES**

`kepss`

This is the Kenya Electronic Payment and Settlement System. It represents bank accounts in Kenya.

**KES**

`nuban`

This means the Nigerian Uniform Bank Account Number. It represents bank accounts in Nigeria.

**NGN**

`basa`

This means the Banking Association South Africa. It represents bank accounts in South Africa.

**ZAR**

`authorization`

This is a unique code that represents a customer’s card. We return an authorization code after a user makes a payment with their card.

**All**

The `recipient_code` from the data object is the unique identifier for a user and would be used to make transfers to that customer This code should be saved with the customer's records in your database.

## [](#generate-a-transfer-reference)Generate a transfer reference

A transfer reference is a unique identifier that lets you track, manage and reconcile each transfer request made on your integration. Transfer references allow you to prevent double crediting as you can retry a non-conclusive transfer rather than initiate a new request.

In order to take advantage of a transfer reference, you need to generate and provide it for every request. When you don’t provide a transfer reference, Paystack generates one for you but this defeats the purpose of the transfer reference.

We recommend [generating a v4 UUID](https://www.uuidgenerator.net/version4) reference of no more than 100 characters. However, if you prefer implementing your own logic, you should ensure your reference contains at least 16 alphanumeric characters.

1{

2	"source": "balance",

3	"reason": "Savings",

4	"amount": 30000,

5	"reference": "your-unique-reference",

6	"recipient": "RCP\_1a25w1h3n0xctjg"

7}

## [](#initiate-a-transfer)Initiate a transfer

To send money to a customer, you make a `POST` request to the [Initate TransferAPI](https://paystack.com/docs/api/transfer#initiate), passing the `reference` and `recipient_code` previously created.

##### Disabling OTP

When building a fully automated system, you might need to disable OTP for transfers. You can disable OTP from the [Preferences tab](https://dashboard.paystack.com/#/settings/preferences) on the Paystack Dashoard. You should uncheck the **Confirm transfers before sending** checkbox as shown in the image below.

[![Image of the disabled OTP state of tranfers](/docs/static/fc8433d173880ec22798a363ba565ab8/8c557/disabled_otp.png)](/docs/static/fc8433d173880ec22798a363ba565ab8/098c1/disabled_otp.png)

cURLNodePHP

Show Response

1curl https://api.paystack.co/transfer

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "source": "balance", 

5      "amount": "37800",

6      "reference": "your-unique-reference", 

7      "recipient": "RCP\_t0ya41mp35flk40", 

8      "reason": "Holiday Flexing" 

9    }'

10\-X POST

11

1{

2  "status": true,

3  "message": "Transfer has been queued",

4  "data": {

5    "reference": "your-unique-reference",

6    "integration": 428626,

7    "domain": "test",

8    "amount": 37800,

9    "currency": "NGN",

10    "source": "balance",

11    "reason": "Holiday Flexing",

12    "recipient": 6788170,

13    "status": "success",

14    "transfer\_code": "TRF\_fiyxvgkh71e717b",

15    "id": 23070321,

16    "createdAt": "2020-05-13T14:22:49.687Z",

17    "updatedAt": "2020-05-13T14:22:49.687Z"

18  }

19}

When you send this request, if there are no errors, the response comes back with a pending status, while the transfer is being processed.

##### Retrying a transfer

If there is an error with the transfer request, **kindly retry the transaction with the same `reference` in order to avoid double crediting**. If a new `reference` is used, the transfer would be treated as a new request.

Test transfers always return success, because there is no processing involved. The live transfers processing usually take between a few seconds and a few minutes. When it's done processing, a notification is sent to your webhook URL.

## [](#verify-a-transfer)Verify a transfer

When a transfer is initiated, it could take a few seconds or minutes to be processed. This is why we recommend relying on webhooks for verification as opposed to polling.

##### Receiving Notifications

In order to receive notifications, you need to [implement a webhook URL](/docs/payments/webhooks/) and set the webhook URL on your [Paystack Dashboard](https://dashboard.paystack.com/#/settings/developer).

Once a transfer is processed, we send the final status of the transfer as a `POST` request to your webhook URL.

Event

Description

`transfer.success`

This is sent when the transfer is successful

`transfer.failed`

This is sent when the transfer fails

`transfer.reversed`

This is sent when we refund a previously debited amount for a transfer that couldn’t be completed

-   Transfer Successful
-   Transfer Failed
-   Transfer Reversed

1{

2  "event": "transfer.success",

3  "data": {

4    "amount": 30000,

5    "currency": "NGN",

6    "domain": "test",

7    "failures": null,

8    "id": 37272792,

9    "integration": {

10      "id": 463433,

11      "is\_live": true,

12      "business\_name": "Boom Boom Industries NG"

13    },

14    "reason": "Have fun...",

15    "reference": "1jhbs3ozmen0k7y5efmw",

16    "source": "balance",

17    "source\_details": null,

18    "status": "success",

19    "titan\_code": null,

20    "transfer\_code": "TRF\_wpl1dem4967avzm",

21    "transferred\_at": null,

22    "recipient": {

23      "active": true,

24      "currency": "NGN",

25      "description": "",

26      "domain": "test",

27      "email": null,

28      "id": 8690817,

29      "integration": 463433,

30      "metadata": null,

31      "name": "Jack Sparrow",

32      "recipient\_code": "RCP\_a8wkxiychzdzfgs",

33      "type": "nuban",

34      "is\_deleted": false,

35      "details": {

36        "account\_number": "**********",

37        "account\_name": null,

38        "bank\_code": "011",

39        "bank\_name": "First Bank of Nigeria"

40      },

41      "created\_at": "2020-09-03T12:11:25.000Z",

42      "updated\_at": "2020-09-03T12:11:25.000Z"

43    },

44    "session": {

45      "provider": null,

46      "id": null

47    },

48    "created\_at": "2020-10-26T12:28:57.000Z",

49    "updated\_at": "2020-10-26T12:28:57.000Z"

50  }

51}

The response for a transfer also contains a **unique transfer code** to identify this transfer. You can use this code to call the [Fetch TransferAPI](https://paystack.com/docs/api/transfer#fetch) endpoint to get the status and details of the transfer.