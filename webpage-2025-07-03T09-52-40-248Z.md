# Payment Channels | Paystack Developer Documentation

# Payment Channels

In A Nutshell

##### In a nutshell

Paystack enables you accept payments from customers using different payment channels such as: cards, mobile money accounts, QR codes, directly from their bank account or USSD.

If you use the the [Popup](/docs/payments/accept-payments/#popup) or [Redirect](/docs/payments/accept-payments/#redirect) method, the paying customer will be shown all the payment methods selected on your [dashboard](https://dashboard.paystack.com/#/settings/preferences). But if you don't want to use either option, you can initiate all the different payment channels directly from your server using the [Charge API](/docs/payments/accept-payments/#charge-api).

##### What channels are available?

Card payment channels are available on all Paystack accounts, while the other payment channels are only available in countries where they are supported.

## [](#cards)Cards

Cards are one of the common payment channels in a lot of countries. We support the following cards across our markets:

Card

Markets

Visa

All

Mastercard

All

Verve

Nigeria

Amex

Nigeria, South Africa and Kenya

##### Feature Availability

The Card API is available in all our markets for businesses that are [PCI Compliant](https://www.pcisecuritystandards.org/). If you intend to use this API, you should check the compliance requirements outlined below and reach out to us.

The Cards API allows you to send card details securely and compliantly to our server from your custom checkout. With this, PCI-DSS complaint businesses can build bespoke checkout experiences without compromising on security.

The sensitivity of card details requires businesses to adhere to the **Payment Card Industry Data Security Standards (PCI-DSS)**, to ensure that they are securely processed. Paystack adheres to this as a [PCI Level 1 Service Provider](https://paystack.com/compliance), allowing non-complaint businesses to use our [Checkout](/docs/payments/accept-payments/#redirect) and [Mobile SDKs](/docs/developer-tools/) for card payments

### [](#compliance-requirements)Compliance Requirements

PCI-DSS certification documents can only be issued on behalf of the PCI Council by an accredited [Qualified Security Assessor](https://listings.pcisecuritystandards.org/assessors_and_solutions/qualified_security_assessors) (QSA) after an audit.

The documents issued by the council are the **Attestation of Compliance** (AOC) and **Report on Compliance.** These documents are only valid for one year from the dated they were signed. We require you to submit these documents before you’re allowed to use this API.

For Paystack, a valid AOC needs to show the following:

1.  Issued after an audit by a QSA
2.  Signed off by a QSA
3.  Within one year of issue date
4.  Has the PCI SSC logo on the cover page
5.  Adheres to at least version 3.2.1 of PCI-DSS

If you have met the criteria above please submit your documents to [<EMAIL>](mailto:<EMAIL>) or through your Paystack relationship manager and we'll grant access to the APIs.

## [](#bank-accounts)Bank accounts

##### Feature availability

This feature is currently available to businesses in Nigeria.

The Pay with Bank feature allows customers pay through internet banking portal or by providing their bank account number and authenticating using an OTP sent to their phone or email.

This is different from Bank Transfers where customers transfer money into a bank account.

### [](#collect-bank-details)Collect bank details

To collect bank details, you would need to prompt the user to select their bank and enter their account number. To fetch the list of supported banks, make a `GET` request to the [list banksAPI](https://paystack.com/docs/api/miscellaneous#bank) endpoint, with the additional filter `pay_with_bank=true`.

The banks can be listed in a dropdown or any other format that allows the user to easily pick their bank of choice.

### [](#create-a-charge)Create a charge

Send `email`, `amount`, `metadata`, `bank` (an object that includes the `code` of the bank and `account_number` supplied by customer) and `birthday` to our Charge endpoint to start.

cURLNodePHP

Show Response

1curl https://api.paystack.co/charge

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "email": "<EMAIL>", 

5      "amount": "10000", 

6      "bank": {

7        "code": "057", 

8        "account\_number": "**********" 

9      }

10    }'

11\-X POST

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "reference": "z8q981z5kp7sfde",

6    "status": "send\_birthday",

7    "display\_text": "Please enter your birthday"

8  }

9}

If the selected bank is Kuda, you need to make use of `phone` and `token` instead of `account_number` in the `bank` object:

cURLNodePHP

Show Response

1curl https://api.paystack.co/charge

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "email": "<EMAIL>", 

5      "amount": "10000", 

6      "bank": {

7        "code": "50211", 

8        "phone": "+*************",

9        "token": "123456"

10      }

11    }'

12\-X POST

13

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "reference": "z8q981z5kp7sfde",

6    "status": "pending",

7    "display\_text": "Processing transaction"

8  }

9}

When the API call is made, the value of the `data.status` key is `pending` as the payment is being processed in the background. The `data.status` then updates to either, `success` or `failed` depending on whether the transaction was successful or not.

## [](#pay-with-transfer)Pay with Transfer

##### Feature availability

This feature is currently available to businesses in Nigeria and merchants need to reach out to [<EMAIL>](mailto:<EMAIL>) to enable it on their integration.

Pay with Transfer (PwT) is a feature that allow merchants or businesses create temporary bank accounts that customers can use to pay for goods or services. The account number is generated and tied to the current customer’s transaction. The account number becomes invalid after the customer’s transaction or when it exceeds it’s expiry time.

### [](#create-a-pwt-charge)Create a PwT charge

At the point of payment, you initiate a request to the [Create ChargeAPI](https://paystack.com/docs/api/charge#create) endpoint, passing the `email`, `amount` and `bank_transfer` object. The `bank_transfer` object takes the `account_expires_at` which is used to set the expiry of an account number for a transaction:

cURLNodePHP

Show Response

1#!/bin/sh

2

3url\="https://api.paystack.co/charge"

4authorization\="Authorization: Bearer YOUR\_SECRET\_KEY"

5content\_type\="Content-Type: application/json"

6data\='{ 

7  "email": "<EMAIL>", 

8  "amount": "10000", 

9  "bank\_transfer": {

10    "account\_expires\_at": "2025-04-24T16:40:57.954Z"

11  } 

12}'

13

14curl "$url" \-H "$authorization" \-H "$content\_type" \-d "$data" \-X POST

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "reference": "kcvu0t3kzs",

6    "status": "pending\_bank\_transfer",

7    "display\_text": "Please make a transfer to the account specified",

8    "account\_name": "TEST-MANAGED-ACCOUNT",

9    "account\_number": "**********",

10    "bank": {

11      "slug": "test-bank",

12      "name": "Test Bank",

13      "id": 24

14    },

15    "account\_expires\_at": "2025-04-24T16:40:57.954Z"

16  }

17}

Bank Transfer Param

Type

Description

`account_expires_at`

String

Account validity period in ISO 8601 format (`YYYY-MM-DDThh:mm:ssZ`). Minimum time is 15 mins from the current time and maximum time is 8 hours from the current time. You can also set this to `null` so we automatically set it to 8 hours from the current time.

##### Account expiry

If the difference between `account_expires_at` and the current time is **less than 15 mins** we will default to a **15 mins**. If the difference between `account_expires_at` and the current time **exceeds 8 hours** we will default to **8 hours**.

If you need to control the transfers your business receives you should implement [Inbound Transfer Approvals](/docs/payments/dedicated-virtual-accounts/#inbound-transfer-approval) . This enables you to reject or accept transfers based on your various business requirements.

### [](#verifying-transfer)Verifying transfer

##### Receiving notifications

To receive notifications, you need to [implement a webhook URL](/docs/payments/webhooks/) and set the webhook URL on your [Paystack Dashboard](https://dashboard.paystack.com/#/settings/developer)

A bank transfer is initiated by a customer and processed by their bank. In order to confirm payment, you need to implement webhooks and listen to the following events:

Event

Description

`charge.success`

This is sent when the customer’s transfer is successful.

`bank.transfer.rejected`

This is sent when the customer either sent an incorrect amount or when the customer has been flagged by our fraud system.

-   Charge Successful
-   Transfer Rejected

1{

2  "event": "charge.success",

3  "data": {

4    "id": **********,

5    "domain": "test",

6    "status": "success",

7    "reference": "zuz8ggd1ro",

8    "amount": 25000,

9    "message": null,

10    "gateway\_response": "Approved",

11    "paid\_at": "2023-09-12T13:29:09.000Z",

12    "created\_at": "2023-09-12T13:27:50.000Z",

13    "channel": "bank\_transfer",

14    "currency": "NGN",

15    "ip\_address": "*************",

16    "metadata": "",

17    "fees\_breakdown": null,

18    "log": null,

19    "fees": 375,

20    "fees\_split": null,

21    "authorization": {

22      "authorization\_code": "AUTH\_q5nfynycgm",

23      "bin": "008XXX",

24      "last4": "X553",

25      "exp\_month": "09",

26      "exp\_year": "2023",

27      "channel": "bank\_transfer",

28      "card\_type": "transfer",

29      "bank": null,

30      "country\_code": "NG",

31      "brand": "Managed Account",

32      "reusable": false,

33      "signature": null,

34      "account\_name": null,

35      "sender\_country": "NG",

36      "sender\_bank": null,

37      "sender\_bank\_account\_number": "XXXXXXX553",

38      "sender\_name": "Jadesola Oluwashina",

39      "narration": "Channel Tests"

40    },

41    "customer": {

42      "id": *********,

43      "first\_name": null,

44      "last\_name": null,

45      "email": "<EMAIL>",

46      "customer\_code": "CUS\_1eq06yu8efl8u63",

47      "phone": null,

48      "metadata": null,

49      "risk\_action": "default",

50      "international\_format\_phone": null

51    },

52    "plan": {},

53    "subaccount": {},

54    "split": {},

55    "order\_id": null,

56    "paidAt": "2023-09-12T13:29:09.000Z",

57    "requested\_amount": 25000,

58    "pos\_transaction\_data": null,

59    "source": {

60      "type": "api",

61      "source": "merchant\_api",

62      "entry\_point": "charge",

63      "identifier": null

64    }

65  }

66}

Alternatively, you can use the [Check Pending ChargeAPI](https://paystack.com/docs/api/charge#check) endpoint to manually verify the status of the transaction.

## [](#ussd)USSD

This Payment method is specifically for Nigerian customers. Nigerian Banks provide USSD services that customers use to perform transactions, and we've integrated with some of them to enable customers complete payments.

The Pay via USSD channel allows your Nigerian customers to pay you by dialling a USSD code on their mobile device. This code is usually in the form of `* followed by some code and ending with #`. The user is prompted to authenticate the transaction with a PIN and then it is confirmed.

All you need to initiate a USSD charge is the customer email and the amount to charge.

When the user pays, a response will be sent to your webhook. Hence, for this to work properly as expected, webhooks must be set up on your Paystack Dashboard.

### [](#create-a-charge-1)Create a charge

Send an email and amount to the [chargeAPI](https://paystack.com/docs/api/charge) endpoint. Specify the USSD type you are charging as well.

Below are all the USSD `types` we support. We'll add to list as we have more:

Bank

Type

Guaranty Trust Bank

737

United Bank of Africa

919

Sterling Bank

822

Zenith Bank

966

cURLNodePHP

Show Response

1curl https://api.paystack.co/charge

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "email": "<EMAIL>", 

5      "amount":"10000",

6      "ussd": {

7        "type": "737"

8      },

9      "metadata": {

10        "custom\_fields":\[{

11          "value": "makurdi",

12          "display\_name": "Donation for",

13          "variable\_name": "donation\_for"

14        }\]

15      }

16    }'

17\-X POST

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "reference": "yjr1r8rwhedara4",

6    "status": "pay\_offline",

7    "display\_text": "Please dial \*737\*33\*4\*18791# on your mobile phone to complete the transaction",

8    "ussd\_code": "\*737\*33\*4\*18791#"

9  }

10}

When a charge is made, the default response provides a USSD code for the customer to dial to complete the payment.

### [](#handle-response)Handle response

When the user completes payment, a response is sent to the merchant’s webhook. Hence, for this to work properly as expected, webhooks must be set up for the merchant..

The `charge.success` event is raised on successful payment. The sample response to be sent to the user’s webhook would look like:

-   JSON

1{

2  "event": "charge.success",

3  "data": {

4    "id": 53561,

5    "domain": "live",

6    "status": "success",

7    "reference": "2ofkbk0yie6dvzb",

8    "amount": 150000,

9    "message": "madePayment",

10    "gateway\_response": "Payment successful",

11    "paid\_at": "2018-06-25T12:42:58.000Z",

12    "created\_at": "2018-06-25T12:38:59.000Z",

13    "channel": "ussd",

14    "currency": "NGN",

15    "ip\_address": "*************, **************, *************",

16    "metadata": "",

17    "log": null,

18    "fees": null,

19    "fees\_split": null,

20    "authorization": {

21      "authorization\_code": "AUTH\_4c6mhnmmeusp4yd",

22      "bin": "XXXXXX",

23      "last4": "XXXX",

24      "exp\_month": "05",

25      "exp\_year": "2018",

26      "channel": "ussd",

27      "card\_type": "offline",

28      "bank": "Guaranty Trust Bank",

29      "country\_code": "NG",

30      "brand": "offline",

31      "reusable": false,

32      "signature": null,

33      "account\_name": null

34    },

35    "customer": {

36      "id": 16200,

37      "first\_name": "John",

38      "last\_name": "Doe",

39      "email": "<EMAIL>",

40      "customer\_code": "CUS\_bpy9ciomcstg55y",

41      "phone": "",

42      "metadata": null,

43      "risk\_action": "default"

44    },

45    "plan": {},

46    "subaccount": {},

47    "paidAt": "2018-06-25T12:42:58.000Z"

48  }

49}

##### USSD recurring charge

Charging returning customers directly is not currently available. Simply call the endpoint to start a new transaction.

## [](#mobile-money)Mobile money

##### Feature Availability

This feature is only available to businesses in Ghana and Kenya.

The Mobile Money channel allows your customers to pay you by using their phone number enabled for mobile money. At the point of payment, the customer is required to authorize the payment on the mobile phones.

Since payment is completed offline, you need to have a [webhook URL](/docs/payments/webhooks/)  which we’ll use to send the final status of the payment to your server.

### [](#create-a-charge-2)Create a charge

To initiate a charge for mobile money, you need to make a `POST` request to the [chargeAPI](https://paystack.com/docs/api/charge) passing the customer’s `email`, `amount`, and `mobile_money` object:

cURLNodePHP

1curl https://api.paystack.co/charge

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "amount": 100,

5      "email": "<EMAIL>",

6      "currency": "GHS",

7      "mobile\_money": {

8        "phone" : "**********",

9        "provider" : "mtn"

10      }

11    }'

12\-X POST

##### Sample code for other providers

This sample code above shows how to charge any MoMo providers. You simply [change the currency](/docs/api/#supported-currency) and replace the `mtn` in the `mobile_money` object with any other provider code shown in the table below.

#### [](#provider-code)Provider code

Here are the character codes for the supported mobile money providers:

Provider

Code

Country

MTN

`mtn`

Ghana and CIV

ATMoney & Airtel Money

`atl`

Ghana and Kenya

Vodafone

`vod`

Ghana

M-PESA

`mpesa`

Kenya

Orange

`orange`

CIV

Wave

`wave`

CIV

All providers, except Vodafone, rely on the customer completing the transaction offline. The `data.status` field will be `pay_offline`, and the customer will be prompted to authorise the transaction on their phones. You should show the customer the `data.display_text` and then listen for the `charge.success` webhook event. The transaction should be completed within **180 seconds**, after which the transactions fail. This is a limitation set by the network providers.

Here is a sample response that requires the customer to complete the process offline:

-   JSON

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "reference": "8nn5fqljd0suybr",

6    "status": "pay\_offline",

7    "display\_text": "Please complete authorization process on your mobile phone"

8  }

9}

##### Transaction Verification

You should call the [Verify TransactionAPI](https://paystack.com/docs/api/transaction#verify) endpoint after the 180 seconds to get the status and reason of the transaction failure. This is found in the `data.message` parameter in the response.

### [](#vodafone)Vodafone

For **Vodafone**, the customer is required to generate a voucher code by dialing the USSD code show in the `data.display_text` field, this voucher code should be collected and passed to the [submit OTPAPI](https://paystack.com/docs/api/charge#submit-otp) endpoint to authorize the transaction.

-   JSON

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "reference": "r13havfcdt7btcm",

6    "status": "send\_otp",

7    "display\_text": "Please dial \*110# to generate a voucher code. Then input the voucher"

8  }

9}

If the mobile money customer enters the otp on time and we are able to get a response just in time, we return the success response:

-   JSON

1{

2  "message": "Charge attempted",

3  "status": true,

4  "data": {

5    "amount": 100,

6    "channel": "mobile\_money",

7    "created\_at": "2018-11-17T14:39:56.000Z",

8    "currency": "GHS",

9    "domain": "live",

10    "fees": 153,

11    "gateway\_response": "Approved",

12    "id": 59333,

13    "ip\_address": "**************, ***************",

14    "message": "madePayment",

15    "paid\_at": "2018-11-17T14:40:18.000Z",

16    "reference": "l7lvu4y3xcka6zu",

17    "status": "success",

18    "transaction\_date": "2018-11-17T14:39:56.000Z",

19    "authorization": {

20      "authorization\_code": "AUTH\_33lz7ev5tq",

21      "bank": "MTN Mobile Money",

22      "bin": "055XXX",

23      "brand": "Mtn mobile money",

24      "channel": "mobile\_money",

25      "country\_code": "GH",

26      "exp\_month": 12,

27      "exp\_year": 9999,

28      "last4": "X149",

29      "reusable": false,

30      "account\_name": null

31    },

32    "customer": {

33      "customer\_code": "CUS\_s3aa4mx0yyvrqye",

34      "email": "<EMAIL>",

35      "id": 16763,

36      "risk\_action": "default"

37    }

38  }

39}

If the transaction is started successfully and the pin is not entered on time, we return this:

-   JSON

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "reference": "84oow6t0rf715g6",

6    "status": "pending"

7  }

8}

### [](#m-pesa)M-PESA

##### Feature Availability

M-PESA allows Kenya-based businesses to charge individual customers and M-PESA Till numbers.

With M-PESA merchants can charge individual users by sending an STK push to the number provided. We recommend that you include the country code in the phone number. For example, `**********` should be sent as `+************` to the [chargeAPI](https://paystack.com/docs/api/charge) endpoint. The customer will be prompted to enter their PIN to complete the transaction.

-   JSON

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "reference": "8nn5fqljd0suybr",

6    "status": "pay\_offline",

7    "display\_text": "Please complete authorization process on your mobile phone"

8  }

9}

#### [](#m-pesa-offline)M-PESA Offline

The offline option allows businesses to create a [chargeAPI](https://paystack.com/docs/api/charge) that will be completed later. This is useful for businesses that offer payment after service completion, for example: restaurants, e-commerce stores, delivery & logistics services. The customer will pay to Paystack's Paybill and the generated `account_reference` will identify the transaction. Another benefit to businesses is the transaction can’t be completed with the wrong amount. It’ll fail and the customer will have to start again.

cURLNodePHP

Show Response

1curl https://api.paystack.co/charge

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "amount": 100,

5      "email": "<EMAIL>",

6      "currency": "KES",

7      "mobile\_money": {

8        "phone": "************",

9        "provider" : "mpesa\_offline"

10      }

11    }'

12\-X POST

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "reference": "e6i9ak3rbq982wh",

6    "status": "pay\_offline",

7    "display\_text": "Please complete authorization process on your mobile phone",

8    "account\_number": "4084333",

9    "account\_reference": 1234567

10  }

11}

The `acount_number` is the Paybill number while the `account_reference` uniquely identifies the transaction. In case you need to change the amount, you should create a new charge and share the new details with the customer. Wrong amounts will lead to transaction failure.

#### [](#m-pesa-till)M-PESA Till

You can also get paid by other businesses from their M-PESA Till numbers. The `mobile_money` object should have `provider` set to `mptill` and the `account` which is the other business's M-PESA Till number. They will receive a prompt on the phone assigned the till for authorization.

cURLNodePHP

1curl https://api.paystack.co/charge

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "amount": 100,

5      "email": "<EMAIL>",

6      "currency": "KES",

7      "mobile\_money": {

8        "account" : "1234567",

9        "provider" : "mptill"

10      }

11    }'

12\-X POST

Both M-PESA Till and individual require the customer to authorise the transaction on their phones. As such you should show them the `display_text` value when building a custom experience.

-   JSON

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "reference": "jq3psd5n96sprwl",

6    "status": "pay\_offline",

7    "display\_text": "Please complete authorization process on your mobile phone"

8  }

9}

##### Transaction Verification

Since M-PESA transactions happen asynchronously, failures due to customer errors aren’t captured easily. We recommend you implement [Verify Transactions](/docs/payments/verify-payments/) when they take too long to get completed.

### [](#handle-response-1)Handle response

When the user completes payment, a response is sent to the merchant’s webhook. Hence, for this to work properly as expected, webhooks must be set up for the merchant.

The `charge.success` event is raised on successful payment. The sample response to be sent to the user’s webhook would look like:

-   JSON

1{

2  "event": "charge.success",

3  "data": {

4    "id": 59214,

5    "domain": "live",

6    "status": "success",

7    "reference": "gf4n3ykzj6a7u89",

8    "amount": 100,

9    "message": "madePayment",

10    "gateway\_response": "Approved",

11    "paid\_at": "2018-11-15T06:10:54.000Z",

12    "created\_at": "2018-11-15T06:10:32.000Z",

13    "channel": "mobile\_money",

14    "currency": "GHS",

15    "ip\_address": "**************, *************",

16    "metadata": "",

17    "log": null,

18    "fees": 153,

19    "fees\_split": null,

20    "authorization": {

21      "authorization\_code": "AUTH\_0aqm8ddx6s",

22      "bin": "055XXX",

23      "last4": "X149",

24      "exp\_month": "12",

25      "exp\_year": "9999",

26      "channel": "mobile\_money",

27      "card\_type": "",

28      "bank": "MTN Mobile Money",

29      "country\_code": "GH",

30      "brand": "Mtn mobile money",

31      "reusable": false,

32      "signature": null,

33      "account\_name": "BoJack Horseman"

34    },

35    "customer": {

36      "id": 16678,

37      "first\_name": "Babafemi",

38      "last\_name": "Aluko",

39      "email": "<EMAIL>",

40      "customer\_code": "CUS\_2jk1i8ezoam49br",

41      "phone": "",

42      "metadata": null,

43      "risk\_action": "allow"

44    },

45    "plan": {},

46    "subaccount": {},

47    "subaccount\_group": {},

48    "paidAt": "2018-11-15T06:10:54.000Z"

49  }

50}

Charging returning customers directly is not currently available. Simply call the endpoint to start a new transaction. We have some [test credentials](/docs/payments/test-payments/#mobile-money) that can be used to run some tests.

## [](#eft)EFT

EFT payments are an instant bank transfer payment method where customers pay merchants through their internet banking interfaces. When the developer specifies an EFT provider, we do a redirect to the providers platform where the customer provides their payment details after which the payment is authorized.

##### Where is this available?

This feature is only available to South African customers.

### [](#create-a-charge-3)Create a charge

You need to send the `email`, `amount`, `currency`, and the EFT provider to the [chargeAPI](https://paystack.com/docs/api/charge) endpoint:

cURLNodePHP

Show Response

1curl https://api.paystack.co/charge

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{

5      "amount": 5000,

6      "currency": "ZAR",

7      "email": "<EMAIL>",

8      "eft": {

9        "provider": "ozow"

10      }

11}'

12\-X POST

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "reference": "18c0ywb63zutno0",

6    "status": "open\_url",

7    "url": "https://crayon.paystack.co/eft/EFT\_OZOW/121502"

8  }

9}

##### Available Providers

Ozow is currently the only provider available.

### [](#handle-response-2)Handle response

When the user completes payment, a response is sent to the merchant’s webhook. The merchant needs to setup webhooks to get the status of the payment. The `charge.success` event is raised on successful payment.

## [](#qr-code)QR code

The QR option generates a QR code that allow customers to use a supported mobile app to complete payments.

When the customer scans the code, they authenticate on a [supported app](/docs/payments/payment-channels/#supported-apps) to complete the payment. When the user pays, a response will be sent to your webhook. This means that you need to implement and set a webhook URL on your Paystack Dashboard.

### [](#create-a-charge-4)Create a charge

Send an email and amount to the [chargeAPI](https://paystack.com/docs/api/charge) endpoint along with a `qr` object. The `qr` object should contain a `provider` parameter, specifying the QR provider for the transaction. The available QR providers are:

Provider

Availability

`scan-to-pay`

South Africa

`visa`

Nigeria

##### Supported Apps

The `scan-to-pay` provider supports both SnapScan and [Scan to Pay (formerly Masterpass) supported apps](/docs/payments/payment-channels/#scan-to-pay) for completing a payment.

cURLNodePHP

Show Response

1curl https://api.paystack.co/charge

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "amount": 100,

5      "email": "<EMAIL>",

6      "currency": "NGN",

7      "qr": {

8        "provider" : "visa"

9      }

10    }'

11\-X POST

1{

2  "status": true,

3  "message": "Charge attempted",

4  "data": {

5    "reference": "48rx32f1womvcr4",

6    "status": "pay\_offline",

7    "qr\_code": "0002010216421527000104176552045499530356654031005802NG5920Babafemi enterprises6005Lagos62230519PSTK\_104176000926|16304713a",

8    "url": "https://files.paystack.co/qr/visa/104176/Babafemi\_enterprises\_visaqr\_1544025482956.png"

9  }

10}

### [](#handle-response-3)Handle response

When the user completes payment, a response is sent to the merchant’s webhook. Hence, for this to work properly as expected, webhooks must be set up for the merchant.

The `charge.success` event is raised on successful payment. The sample response to be sent to the user’s webhook would look like:

-   JSON

1{

2  "event": "charge.success",

3  "data": {

4    "id": 59565,

5    "domain": "test",

6    "status": "success",

7    "reference": "48rx32f1womvcr4",

8    "amount": 10000,

9    "message": "madePayment",

10    "gateway\_response": "Payment successful",

11    "paid\_at": "2018-12-05T15:58:45.000Z",

12    "created\_at": "2018-12-05T15:58:02.000Z",

13    "channel": "qr",

14    "currency": "NGN",

15    "ip\_address": "************, ***************",

16    "metadata": "",

17    "log": null,

18    "fees": null,

19    "fees\_split": null,

20    "authorization": {

21      "authorization\_code": "AUTH\_2b4zs69fgy7qflh",

22      "bin": "483953",

23      "last4": "6208",

24      "exp\_month": "12",

25      "exp\_year": "2018",

26      "channel": "qr",

27      "card\_type": "DEBIT",

28      "bank": "Visa QR",

29      "country\_code": "NG",

30      "brand": "VISA",

31      "reusable": false,

32      "signature": null,

33      "account\_name": "BoJack Horseman"

34    },

35    "customer": {

36      "id": 16787,

37      "first\_name": "I",

38      "last\_name": "SURRENDER",

39      "email": "<EMAIL>",

40      "customer\_code": "CUS\_ehg851zbxon0bvx",

41      "phone": "",

42      "metadata": null,

43      "risk\_action": "default"

44    },

45    "plan": {},

46    "subaccount": {},

47    "subaccount\_group": {},

48    "paidAt": "2018-12-05T15:58:45.000Z"

49  }

50}

##### QR code recurring charge

Charging returning customers directly is currently not available. You need to call the endpoint to start a new transaction.

### [](#supported-apps)Supported Apps

In order to complete a payment, your customers can scan or enter the code in a supported application. Here are the supported applications by providers:

#### [](#visa)Visa

Customers can scan Visa QR codes from the following banking apps:

-   Ecobank
-   First Bank
-   Fidelity Bank
-   Access Bank
-   Access (Diamond) Bank
-   Zenith Bank

#### [](#snapscan)SnapScan

Customers can complete a payment in a snap by scanning the QR code with their SnapScan iOS or Android app.

#### [](#scan-to-pay)Scan to Pay

Customers can use Scan to Pay (formerly Masterpass) QR codes from any of the mobile apps listed below:

Banking Apps

Wallets

Standalone Scan to Pay

Standard Bank

Ukheshe

Nedbank Scan to Pay

FNB Banking

Spot (by Virgin Money)

Standard Bank Scan to Pay

Nedbank Money

Vodapay

Absa Scan to Pay

Capitec Bank

Telkom Pay

Absa

Instapay

RMB

Nedbank Avo