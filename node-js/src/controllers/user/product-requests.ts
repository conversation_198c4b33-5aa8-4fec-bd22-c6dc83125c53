import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  createProductRequestService,
  deleteProductRequestService,
  getProductRequestsService,
} from "~/services/user/product-requests";
import {
  createProductRequestBodySchema,
  deleteProductRequestParamsSchema,
  getProductRequestsQuerySchema,
} from "~/validators/user/product-requests";

async function getProductRequests(request: Request, response: Response) {
  try {
    const {
      page,
      limit,
      sort,
      name,
      minQuantity,
      minPrice,
      maxPrice,
      categoryId,
    } = getProductRequestsQuerySchema.parse(request.query);

    const {
      productRequests,
      total,
      pages,
      limit: responseLimit,
      page: responsePage,
    } = await getProductRequestsService({
      userId: request.user.id,
      page,
      limit,
      sort,
      name,
      minQuantity,
      minPrice,
      maxPrice,
      categoryId,
    });

    return response.success(
      {
        data: { productRequests },
        meta: { total, pages, limit: responseLimit, page: responsePage },
      },
      {
        message: "Product requests fetched successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function createProductRequest(request: Request, response: Response) {
  try {
    const validatedData = createProductRequestBodySchema.parse(request.body);

    const { productRequest } = await createProductRequestService({
      userId: request.user.id,
      data: validatedData,
      files: request.files as Express.Multer.File[],
    });

    return response.success(
      {
        data: { productRequest },
      },
      {
        message: "Product request created successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function deleteProductRequest(request: Request, response: Response) {
  try {
    const { id } = deleteProductRequestParamsSchema.parse(request.params);

    const { productRequest } = await deleteProductRequestService({
      userId: request.user.id,
      requestId: id,
    });

    return response.success(
      {
        data: { productRequest },
      },
      {
        message: "Product request deleted successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { createProductRequest, deleteProductRequest, getProductRequests };
