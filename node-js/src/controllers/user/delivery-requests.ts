import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  createDeliveryRequestService,
  getDeliveryRequestService,
  getDeliveryRequestsService,
} from "~/services/user/delivery-requests";
import {
  createDeliveryRequestBodySchema,
  createDeliveryRequestParamsSchema,
  getDeliveryRequestParamsSchema,
  getDeliveryRequestsQuerySchema,
} from "~/validators/user/delivery-requests";

async function getDeliveryRequests(request: Request, response: Response) {
  try {
    const { page, limit, sort, minPrice, maxPrice, logisticProviderId } =
      getDeliveryRequestsQuerySchema.parse(request.query);

    const {
      deliveryRequests,
      total,
      pages,
      limit: responseLimit,
      page: responsePage,
    } = await getDeliveryRequestsService({
      userId: request.user.id,
      page,
      limit,
      sort,
      minPrice,
      maxPrice,
      logisticProviderId,
    });

    return response.success(
      {
        data: { deliveryRequests },
        meta: { total, pages, limit: responseLimit, page: responsePage },
      },
      {
        message: "Delivery requests fetched successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function getDeliveryRequest(request: Request, response: Response) {
  try {
    const { orderId } = getDeliveryRequestParamsSchema.parse(request.params);

    const { deliveryRequest } = await getDeliveryRequestService({
      userId: request.user.id,
      orderId,
    });

    return response.success(
      {
        data: { deliveryRequest },
      },
      {
        message: "Delivery request fetched successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function createDeliveryRequest(request: Request, response: Response) {
  try {
    const { orderId } = createDeliveryRequestParamsSchema.parse(request.params);
    const { price } = createDeliveryRequestBodySchema.parse(request.body);

    const { deliveryRequest } = await createDeliveryRequestService({
      userId: request.user.id,
      orderId,
      price,
    });

    return response.success(
      {
        data: { deliveryRequest },
      },
      {
        message: "Delivery request created successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { getDeliveryRequests, getDeliveryRequest, createDeliveryRequest };
