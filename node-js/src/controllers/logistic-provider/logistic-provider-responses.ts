import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import { createLogisticProviderResponseService } from "~/services/logistic-provider/logistic-provider-responses";
import {
  createLogisticProviderResponsesBodySchema,
  createLogisticProviderResponsesParamsSchema,
} from "~/validators/logistic-provider/logistic-provider-responses";

async function createLogisticProviderResponse(
  request: Request,
  response: Response,
) {
  try {
    const { deliveryRequestId } =
      createLogisticProviderResponsesParamsSchema.parse(request.params);
    const { price } = createLogisticProviderResponsesBodySchema.parse(
      request.body,
    );

    const { logisticProviderResponse } =
      await createLogisticProviderResponseService({
        logisticProviderId: request.user.id,
        deliveryRequestId,
        price,
      });

    return response.success(
      {
        data: { logisticProviderResponse },
      },
      {
        message: "Logistic provider response created successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { createLogisticProviderResponse };
