import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import { getProductRequestsService } from "~/services/vendor/product-requests";
import { getProductRequestsQuerySchema } from "~/validators/vendor/product-requests";

async function getProductRequests(request: Request, response: Response) {
  try {
    const {
      page,
      limit,
      sort,
      name,
      minQuantity,
      minPrice,
      maxPrice,
      categoryId,
    } = getProductRequestsQuerySchema.parse(request.query);

    const {
      productRequests,
      total,
      pages,
      limit: responseLimit,
      page: responsePage,
    } = await getProductRequestsService({
      vendorId: request.user.id,
      page,
      limit,
      sort,
      name,
      minQuantity,
      minPrice,
      maxPrice,
      categoryId,
    });

    return response.success(
      {
        data: { productRequests },
        meta: { total, pages, limit: responseLimit, page: responsePage },
      },
      {
        message: "Product requests fetched successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { getProductRequests };
