import { Router } from "express";

import { deliveryRequestsRouter } from "~/routers/user/delivery-requests";
import { logisticProviderResponsesRouter } from "~/routers/user/logistic-provider-responses";
import { ordersRouter } from "~/routers/user/orders";
import { productRequestsRouter } from "~/routers/user/product-requests";
import { profileRouter } from "~/routers/user/profile";
import { reviewsRouter } from "~/routers/user/reviews";

const userRouter = Router();

userRouter.use("/delivery-requests", deliveryRequestsRouter);
userRouter.use("/logistic-provider-responses", logisticProviderResponsesRouter);
userRouter.use("/orders", ordersRouter);
userRouter.use("/product-requests", productRequestsRouter);
userRouter.use("/profile", profileRouter);
userRouter.use("/reviews", reviewsRouter);

export { userRouter };
