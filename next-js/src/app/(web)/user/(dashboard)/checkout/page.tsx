"use client";

import type {
  PublicOrderType,
  SingleResponseType,
  UserProfileType,
} from "~/lib/types";

import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

import { useStore } from "@nanostores/react";
import { useMutation, useQuery } from "@tanstack/react-query";

import axios, { AxiosError } from "axios";
import {
  Loader2Icon,
  MapPinIcon,
  ShoppingCartIcon,
  UserIcon,
} from "lucide-react";
import { toast } from "sonner";

import { PageLayout } from "~/components/common/page-layout";
import { EmptyState } from "~/components/layout/empty-state";
import { Button } from "~/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Separator } from "~/components/ui/separator";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import { DeliveryOption } from "~/lib/types";
import { cn, formatPrice } from "~/lib/utils";
import { $cart } from "~/stores/cart";

async function getUserProfile({ token }: { token: string | null }) {
  const response = await axios.get(routes.api.user.profile.url(), {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return response.data;
}

async function createOrder({
  token,
  products,
  deliveryOption,
}: {
  token: string | null;
  products: {
    productId: string;
    quantity: number;
  }[];
  deliveryOption: DeliveryOption;
}): Promise<SingleResponseType<{ order: PublicOrderType }>> {
  if (!token) {
    throw new Error("Authentication token is required");
  }

  const response = await axios.post(
    routes.api.user.orders.url(),
    { products, deliveryOption },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  return response.data;
}

export default function CheckoutPage() {
  const { token, auth } = useAuthContext();

  const router = useRouter();
  const cart = useStore($cart);

  const [deliveryOption, setDeliveryOption] = useState<DeliveryOption>(
    DeliveryOption.SELF_PICKUP,
  );

  useEffect(() => {
    if (cart.items.length === 0) {
      router.push(routes.app.user.productRequests.url());
    }
  }, [cart.items.length, router]);

  const { totalPrice } = useMemo(() => {
    let quantity = 0;
    let price = 0;

    for (const item of cart.items) {
      quantity += item.quantity;
      const itemPrice = item.salePrice ?? item.price;
      price += itemPrice * item.quantity;
    }

    return { totalQuantity: quantity, totalPrice: price };
  }, [cart.items]);

  const {
    data: profileQuery,
    isLoading: profileQueryIsLoading,
    isError: profileQueryIsError,
  } = useQuery<
    SingleResponseType<{
      profile: UserProfileType;
    }>
  >({
    queryKey: ["profile"],
    queryFn: () => getUserProfile({ token }),
    enabled: !!token && auth?.role === "USER",
  });

  const createOrderMutation = useMutation({
    mutationFn: async (data: {
      products: { productId: string; quantity: number }[];
      deliveryOption: DeliveryOption;
    }) => {
      return createOrder({
        token,
        products: data.products,
        deliveryOption: data.deliveryOption,
      });
    },
    onSuccess: ({ info }) => {
      toast.success(info.message || "Order placed successfully!");

      $cart.set({ items: [] });

      router.push(routes.app.user.orders.url());
    },
    onError: (error: AxiosError<{ info: { message: string } }>) => {
      if (error instanceof AxiosError) {
        toast.error(
          error.response?.data.info?.message ||
            "Failed to place order. Please try again.",
        );
      } else {
        toast.error("An unexpected error occurred. Please try again.");
      }
    },
  });

  const handlePlaceOrder = () => {
    if (!token || auth?.role !== "USER") {
      toast.error("Please log in to place an order");
      router.push(routes.app.auth.signIn.url());
      return;
    }

    const products = cart.items.map((item) => ({
      productId: item.id,
      quantity: item.quantity,
    }));

    createOrderMutation.mutate({
      products,
      deliveryOption,
    });
  };

  if (cart.items.length === 0) {
    return (
      <PageLayout
        title="Checkout"
        description="Review your order and complete your purchase"
      >
        <EmptyState
          icon={ShoppingCartIcon}
          title="Your cart is empty"
          description="Add some products to your cart to checkout"
          action={{
            label: "Browse Products",
            onClick: () => router.push(routes.app.public.products.url()),
          }}
        />
      </PageLayout>
    );
  }

  return (
    <PageLayout
      title="Checkout"
      description="Review your order and complete your purchase"
      isLoading={profileQueryIsLoading}
      isError={profileQueryIsError}
      errorTitle="Error Loading Profile"
      errorDescription="We couldn't load your profile information. Please try again."
      errorAction={{
        label: "Retry",
        onClick: () => router.refresh(),
      }}
    >
      <div className={cn("flex flex-col lg:flex-row gap-6 items-start")}>
        <Card className={cn("w-full")}>
          <CardHeader>
            <CardTitle>
              <h3 className={cn("text-2xl font-bold")}>Order Summary</h3>
            </CardTitle>
            <CardDescription>
              <p className={cn("text-muted-foreground text-sm font-medium")}>
                Review your order details before checkout
              </p>
            </CardDescription>
          </CardHeader>
          <CardContent className={cn("space-y-6")}>
            <div className={cn("space-y-4")}>
              {cart.items.map((item) => {
                const itemPrice = item.salePrice ?? item.price;
                const itemSubtotal = itemPrice * item.quantity;
                return (
                  <div key={item.id} className={cn("flex items-start gap-4")}>
                    <div
                      className={cn(
                        "relative size-20 flex-shrink-0 overflow-hidden rounded-md border",
                      )}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_FILE_URL}/${item.pictureIds[0]}`}
                        alt={item.name}
                        fill
                        sizes="80px"
                        className={cn("object-cover")}
                      />
                    </div>
                    <div className={cn("flex-grow space-y-1")}>
                      <p className={cn("font-medium text-base line-clamp-2")}>
                        {item.name}
                      </p>
                      <p className={cn("text-sm text-muted-foreground")}>
                        Quantity: {item.quantity} × {formatPrice(itemPrice)}
                      </p>
                      <p className={cn("font-medium text-base")}>
                        {formatPrice(itemSubtotal)}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>

            <Separator />

            <div className={cn("space-y-2")}>
              <div className={cn("flex justify-between font-bold text-base")}>
                <p>Total</p>
                <p>{formatPrice(totalPrice)}</p>
              </div>
            </div>

            <div className={cn("space-y-4")}>
              <div className={cn("space-y-2")}>
                <p className={cn("font-medium text-sm")}>Delivery Option</p>
                <Select
                  value={deliveryOption}
                  onValueChange={(value) =>
                    setDeliveryOption(value as DeliveryOption)
                  }
                >
                  <SelectTrigger className={cn("w-full")}>
                    <SelectValue placeholder="Select delivery option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={DeliveryOption.SELF_PICKUP}>
                      Self Pickup
                    </SelectItem>
                    <SelectItem value={DeliveryOption.LOGISTIC}>
                      Logistic Provider
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p className={cn("text-xs text-muted-foreground")}>
                  {deliveryOption === DeliveryOption.SELF_PICKUP
                    ? "You'll need to pick up your order from our location."
                    : "Your order will be delivered to your address."}
                </p>
              </div>

              <Button
                className={cn("w-full")}
                variant="default-gradient"
                size="lg"
                onClick={handlePlaceOrder}
                disabled={createOrderMutation.isPending}
              >
                {createOrderMutation.isPending && (
                  <Loader2Icon className={cn("mr-2 size-4 animate-spin")} />
                )}
                Place Order
              </Button>
              <p
                className={cn("mt-2 text-sm text-center text-muted-foreground")}
              >
                By placing your order, you agree to our{" "}
                <Link
                  href="#"
                  className={cn(
                    "underline underline-offset-4 hover:text-primary",
                  )}
                >
                  Terms and Conditions
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className={cn("w-full")}>
          <CardHeader className="pb-2 sm:pb-4">
            <CardTitle>
              <h3 className={cn("text-2xl font-bold")}>Customer Information</h3>
            </CardTitle>
            <CardDescription>
              <p className={cn("text-muted-foreground text-sm font-medium")}>
                Your account and contact details
              </p>
            </CardDescription>
          </CardHeader>
          <CardContent>
            {!token || auth?.role !== "USER" ? (
              <div className="space-y-4">
                <div className="bg-muted/50 p-3 sm:p-4 rounded-lg border border-border">
                  <p className="text-xs sm:text-sm mb-2">
                    Please sign in to complete your purchase
                  </p>
                  <Button
                    asChild
                    variant="default-gradient"
                    size="sm"
                    className="sm:text-sm"
                  >
                    <Link href={routes.app.auth.signIn.url()}>Sign In</Link>
                  </Button>
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground">
                  <p>Don't have an account?</p>
                  <Button
                    variant="link"
                    asChild
                    className="p-0 h-auto text-xs sm:text-sm"
                  >
                    <Link href={routes.app.auth.signUp.url()}>
                      Create an account
                    </Link>
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4 sm:space-y-6">
                <div className="grid grid-cols-1 gap-3 sm:gap-4">
                  <div className="bg-muted/50 p-3 sm:p-4 rounded-lg border border-border">
                    <div className="flex items-center mb-2 sm:mb-3">
                      <UserIcon className="size-4 sm:h-5 sm:w-5 mr-2 text-primary" />
                      <h4 className="font-medium text-sm sm:text-base">
                        Personal Details
                      </h4>
                    </div>
                    <div className="grid grid-cols-1 gap-1 sm:gap-2 pl-6 sm:pl-7">
                      <div className="flex items-start flex-wrap sm:flex-nowrap">
                        <p className="text-xs sm:text-sm font-medium text-muted-foreground w-20 sm:w-24">
                          Name:
                        </p>
                        <p className="text-xs sm:text-sm">
                          {profileQuery?.data?.profile.name}
                        </p>
                      </div>
                      <div className="flex items-start flex-wrap sm:flex-nowrap">
                        <p className="text-xs sm:text-sm font-medium text-muted-foreground w-20 sm:w-24">
                          Phone:
                        </p>
                        <p className="text-xs sm:text-sm">
                          {profileQuery?.data?.profile.phone}
                        </p>
                      </div>
                      <div className="flex items-start flex-wrap sm:flex-nowrap">
                        <p className="text-xs sm:text-sm font-medium text-muted-foreground w-20 sm:w-24">
                          Email:
                        </p>
                        <p className="text-xs sm:text-sm">{auth?.email}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-muted/50 p-3 sm:p-4 rounded-lg border border-border">
                    <div className="flex items-center mb-2 sm:mb-3">
                      <MapPinIcon className="size-4 sm:h-5 sm:w-5 mr-2 text-primary" />
                      <h4 className="font-medium text-sm sm:text-base">
                        Delivery Address
                      </h4>
                    </div>
                    <div className="grid grid-cols-1 gap-1 sm:gap-2 pl-6 sm:pl-7">
                      <div className="flex items-start flex-wrap sm:flex-nowrap">
                        <p className="text-xs sm:text-sm font-medium text-muted-foreground w-20 sm:w-24">
                          Address:
                        </p>
                        <p className="text-xs sm:text-sm">
                          {profileQuery?.data?.profile.deliveryAddress}
                        </p>
                      </div>
                      <div className="flex items-start flex-wrap sm:flex-nowrap">
                        <p className="text-xs sm:text-sm font-medium text-muted-foreground w-20 sm:w-24">
                          City:
                        </p>
                        <p className="text-xs sm:text-sm">
                          {profileQuery?.data?.profile.city}
                        </p>
                      </div>
                      <div className="flex items-start flex-wrap sm:flex-nowrap">
                        <p className="text-xs sm:text-sm font-medium text-muted-foreground w-20 sm:w-24">
                          Postal Code:
                        </p>
                        <p className="text-xs sm:text-sm">
                          {profileQuery?.data?.profile.postalCode}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <Button
                    variant="outline"
                    size="sm"
                    asChild
                    className="w-full text-xs sm:text-sm"
                  >
                    <Link href={routes.app.user.settings.url()}>
                      Update Profile
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </PageLayout>
  );
}
