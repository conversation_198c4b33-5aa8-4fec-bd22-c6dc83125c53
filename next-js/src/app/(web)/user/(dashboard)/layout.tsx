"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

import { useStore } from "@nanostores/react";

import {
  ClipboardListIcon,
  PackageIcon,
  ShoppingCartIcon,
  UserIcon,
} from "lucide-react";

import type { PropsWithChildren } from "react";
import { buttonVariants } from "~/components/ui/button";
import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";
import { $cart } from "~/stores/cart";

export default function DashboardLayout({
  children,
}: Readonly<PropsWithChildren>) {
  const pathname = usePathname();
  const cart = useStore($cart);
  const hasItemsInCart = cart.items.length > 0;

  const baseNavItems = [
    {
      href: routes.app.user.orders.url(),
      label: routes.app.user.orders.label,
      icon: PackageIcon,
      active:
        pathname === routes.app.user.orders.url() ||
        pathname.startsWith(routes.app.user.logistic.url()),
    },
    {
      href: routes.app.user.settings.url(),
      label: routes.app.user.settings.label,
      icon: UserIcon,
      active: pathname === routes.app.user.settings.url(),
    },
  ];

  const userNavItems = [
    ...baseNavItems.slice(0, 1),
    hasItemsInCart
      ? {
          href: routes.app.user.checkout.url(),
          label: routes.app.user.checkout.label,
          icon: ShoppingCartIcon,
          active: pathname === routes.app.user.checkout.url(),
        }
      : {
          href: routes.app.user.productRequests.url(),
          label: routes.app.user.productRequests.label,
          icon: ClipboardListIcon,
          active: pathname === routes.app.user.productRequests.url(),
        },
    ...baseNavItems.slice(1),
  ];

  return (
    <>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        <nav>
          <ul className="flex flex-wrap items-center gap-4 sm:gap-6 lg:gap-8">
            {userNavItems.map((item) => (
              <li
                key={item.href}
                className="flex-1 min-w-[100px] sm:min-w-[150px] lg:min-w-[200px]"
              >
                <Link
                  href={item.href}
                  className={cn(
                    buttonVariants({
                      variant: item.active ? "default-gradient" : "ghost",
                      size: "lg",
                      className: "w-full",
                    }),
                  )}
                >
                  <item.icon className="size-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6" />
                  <span className="text-xs sm:text-sm lg:text-base">
                    {item.label}
                  </span>
                </Link>
              </li>
            ))}
          </ul>
          <div className="border-b mt-2" />
        </nav>
        {children}
      </div>
    </>
  );
}
