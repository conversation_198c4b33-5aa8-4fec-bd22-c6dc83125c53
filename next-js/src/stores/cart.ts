import type {
  CartItemType,
  PublicCategoryType,
  VendorProfileType,
} from "~/lib/types";

import { persistentMap } from "@nanostores/persistent";

export const $cart = persistentMap<{
  items: (CartItemType & {
    category: PublicCategoryType;
    vendor: VendorProfileType;
  })[];
}>(
  "ecobuiltconnect-cart:",
  {
    items: [],
  },
  {
    encode: JSON.stringify,
    decode: JSON.parse,
  },
);
