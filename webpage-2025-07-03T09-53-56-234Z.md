# Test Payments | Paystack Developer Documentation

# Test Payments

You can use the following test details to test different payment channels.

## [](#cards)Cards

##### Card expiry date

The expiry date for each card can be any date in the future.

### [](#successful-cards)Successful Cards

No validation(reusable)

4084 0840 8408 4081

Expiry07/26

CVV408

PIN validation

5078 5078 5078 5078 12

Expiry07/26

CVV081

Pin1111

PIN + OTP validation

5060 6666 6666 6666 666

Expiry07/26

CVV123

Pin1234

OTP123456

PIN + Phone + OTP validation

5078 5078 5078 5078 04

Expiry07/26

CVV884

Pin0000

OTP123456

Bank Auth Simulation(reusable)

4084 0800 0000 0409

Expiry07/26

CVV000

### [](#failed-cards)Failed Cards

Declined

4084 0800 0000 5408

Expiry07/26

CVV001

Token Not Generated

5078 5078 5078 5078 53

Expiry07/26

CVV082

Pin0000

### [](#api-errors)API Errors

Insufficent funds

4084 0800 0067 0037

Expiry07/26

CVV787

## [](#bank-accounts)Bank Accounts

##### Nigerian merchants

Nigerian merchants can use a regular bank account to create a transfer recipient when testing in test mode.

Zenith Bank(transaction)

000 000 000 0

Birthday1998-07-03

OTP123456

Zenith Bank(transfer)

000 000 000 0

Code057

Kuda Bank

+234 810 000 000 0

Code50211

Token123456

## [](#mobile-money)Mobile Money

No PIN/OTP

055 123 498 7

NetworkMTN

No PIN/OTP

+254 710 000 000

NetworkM-Pesa

CIV - Orange

070 000 000 0

NetworkOrange

OTP1234

## [](#dedicated-virtual-account)Dedicated Virtual Account

You can make use of the [sample bank application](https://demobank.paystackintegrations.com/) we created to initiate a transfer to your test virtual account. If you simply want to try out the [dedicated virtual account](/docs/payments/dedicated-virtual-accounts/) product, kindly make use of the test account below:

Demo Bank

123 000 164 4

Pin0000