# Refunds | Paystack Developer Documentation

# Refunds

In A Nutshell

##### In a nutshell

You can repay your customer for a previous transaction in part or fully by initiating a refund and listening to notifications.

## [](#getting-started)Getting started

Sometimes, a customer makes a request for their money after a successful transaction. Other times, an order cannot be fulfilled after payment has been made. In either case, you need to consider if you should initiate a:

1.  Partial refund or,
2.  Full refund

Our [RefundAPI](https://paystack.com/docs/api/refund) endpoints allow you to repay your customers in part or fully. You simply initiate a refund request and we keep you updated on the status of the refund.

## [](#create-a-refund)Create a refund

To initiate a refund, you make a `POST` request to the [Create RefundAPI](https://paystack.com/docs/api/refund#create) and pass the transaction ID or reference in the `transaction` field. If an amount isn't passed, we handle the request as a full refund.

If you want to do a partial refund, you pass an `amount` parameter with the amount to refund. The refund amount must not be more than the original transaction amount.

cURLNodePHP

Show Response

1curl https://api.paystack.co/refund

2\-H 'authorization: Bearer YOUR\_SECRET\_KEY'

3\-H 'cache-control: no-cache'

4\-H 'content-type: application/json'

5\-d '{ "transaction":"qufywna9w9a5d8v", "amount":"10000" }'

6\-X POST

1{

2  "status": true,

3  "message": "Refund has been queued for processing",

4  "data": {

5    "transaction": {

6      "id": **********,

7      "domain": "live",

8      "reference": "T685312322670591",

9      "amount": 10000,

10      "paid\_at": "2021-08-20T18:34:11.000Z",

11      "channel": "apple\_pay",

12      "currency": "NGN",

13      "authorization": {

14        "exp\_month": null,

15        "exp\_year": null,

16        "account\_name": null

17      },

18      "customer": {

19        "international\_format\_phone": null

20      },

21      "plan": {},

22      "subaccount": {

23        "currency": null

24      },

25      "split": {},

26      "order\_id": null,

27      "paidAt": "2021-08-20T18:34:11.000Z",

28      "pos\_transaction\_data": null,

29      "source": null,

30      "fees\_breakdown": null

31    },

32    "integration": 412829,

33    "deducted\_amount": 0,

34    "channel": null,

35    "merchant\_note": "Refund for transaction T685312322670591 by <EMAIL>",

36    "customer\_note": "Refund for transaction T685312322670591",

37    "status": "pending",

38    "refunded\_by": "<EMAIL>",

39    "expected\_at": "2021-12-16T09:21:17.016Z",

40    "currency": "NGN",

41    "domain": "live",

42    "amount": 10000,

43    "fully\_deducted": false,

44    "id": 3018284,

45    "createdAt": "2021-12-07T09:21:17.122Z",

46    "updatedAt": "2021-12-07T09:21:17.122Z"

47  }

48}

##### Maximum refund amount

The refund amount must not be more than the original transaction amount.

## [](#list-refunds)List Refunds

To pull a list of your refunds, you can use the [List RefundsAPI](https://paystack.com/docs/api/refund#list) to fetch all your refunds.

cURLNodePHP

Show Response

1curl https://api.paystack.co/refund 

2\-H 'authorization: Bearer YOUR\_SECRET\_KEY'

3\-H 'cache-control: no-cache'

4\-H 'content-type: application/json' 

5\-X GET

1{

2  "status": true,

3  "message": "Refunds retrieved",

4  "data": \[

5    {

6      "integration": 428626,

7      "transaction": 627178582,

8      "dispute": null,

9      "settlement": null,

10      "id": 747680,

11      "domain": "test",

12      "currency": "NGN",

13      "amount": 10000,

14      "status": "processed",

15      "refunded\_at": null,

16      "refunded\_by": "<EMAIL>",

17      "customer\_note": "Refund for transaction qufywna9w9a5d8v",

18      "merchant\_note": "Refund for transaction qufywna9w9a5d8<NAME_EMAIL>",

19      "deducted\_amount": 10000,

20      "fully\_deducted": true,

21      "createdAt": "2020-05-19T11:12:17.000Z"

22    },

23    {

24      "integration": 428626,

25      "transaction": 640672957,

26      "dispute": null,

27      "settlement": null,

28      "id": 742609,

29      "domain": "test",

30      "currency": "NGN",

31      "amount": 20000,

32      "status": "processed",

33      "refunded\_at": null,

34      "refunded\_by": "<EMAIL>",

35      "customer\_note": "blah blah",

36      "merchant\_note": "yada yada",

37      "deducted\_amount": 20000,

38      "fully\_deducted": true,

39      "createdAt": "2020-04-30T10:43:47.000Z"

40    }

41  \],

42  "meta": {

43    "total": 2,

44    "skipped": 0,

45    "perPage": 50,

46    "page": 1,

47    "pageCount": 1

48  }

49}

## [](#refund-status)Refund status

During the lifecycle of a refund, its status changes based on the actions performed by the refund processor. When the status of a refund changes, the status of the associated transaction follows suit.

The table below shows the relationship between the status of a refund and its associated transaction:

Status

Description

Transaction Status

`pending`

Refund initiated, waiting for response from the processor

Reversal Pending

`processing`

Refund has been received by the processor.

Reversal Pending

`failed`

Refund cannot be processed. Your account is credited with refund amount.

Success

`processed`

Refund has successfully been processed by the processor.

Reversed

##### Processed Refunds

When a refund is marked as `processed`, it may still take up to 10 business days for customers to receive their funds.

## [](#listen-to-notifications)Listen to notifications

##### Receiving Notifications

In order to receive notifications, you need to [implement a webhook URL](/docs/payments/webhooks/) and set the webhook URL in your [Paystack dashboard](https://dashboard.paystack.com/#/settings/developer).

We send different events based on the state of a refund. You can listen to the following events to stay updated on the state of a customer's refund:

Event

Description

`refund.pending`

This is sent when a refund is initiated and we are waiting for a response from the processor.

`refund.processing`

This is sent when the refund has been received by the processor.

`refund.failed`

This is sent when a refund cannot be processed. Your account is credited with the refund amount.

`refund.processed`

This is sent when the refund has been successfully processed by the processor.

-   Refund Pending
-   Refund Processing
-   Refund Processed
-   Refund Failed

1{

2  "event": "refund.pending",

3  "data": {

4    "status": "pending",

5    "transaction\_reference": "tvunjbbd\_412829\_4b18075d\_c7had",

6    "refund\_reference": null,

7    "amount": "10000",

8    "currency": "NGN",

9    "processor": "instant-transfer",

10    "customer": {

11      "first\_name": "Drew",

12      "last\_name": "Berry",

13      "email": "<EMAIL>"

14    },

15    "integration": 412829,

16    "domain": "live"

17  }

18}