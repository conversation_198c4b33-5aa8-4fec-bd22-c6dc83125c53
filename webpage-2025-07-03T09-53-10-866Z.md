# Split Payments | Paystack Developer Documentation

# Split Payments

In A Nutshell

##### In a nutshell

With split payments you can share your settlement for a transaction with another account

Implementing split payments involves:

1.  [Create a subaccount](/docs/payments/split-payments/#create-a-subaccount)
2.  [Initialize a split payment](/docs/payments/split-payments/#initialize-a-split-payment)

## [](#create-a-subaccount)Create a subaccount

Subaccounts can be created via the [Paystack Dashboard](https://dashboard.paystack.com/#/subaccounts) or using the [create subaccountAPI](https://paystack.com/docs/api/subaccount#create) endpoint. When a subaccount is created, the `subaccount_code` is returned.

cURLNodePHP

Show Response

1curl https://api.paystack.co/subaccount

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "business\_name": "Oasis", 

5      "bank\_code": "058", 

6      "account\_number": "**********", 

7      "percentage\_charge": 30 

8    }'

9\-X POST

1{

2  "status": true,

3  "message": "Subaccount created",

4  "data": {

5    "business\_name": "Oasis",

6    "account\_number": "**********",

7    "percentage\_charge": 30,

8    "settlement\_bank": "Guaranty Trust Bank",

9    "currency": "NGN",

10    "bank": 9,

11    "integration": 463433,

12    "domain": "test",

13    "account\_name": "LARRY JAMES  O",

14    "product": "collection",

15    "managed\_by\_integration": 463433,

16    "subaccount\_code": "ACCT\_6uujpqtzmnufzkw",

17    "is\_verified": false,

18    "settlement\_schedule": "AUTO",

19    "active": true,

20    "migrate": false,

21    "id": 1151727,

22    "createdAt": "2024-08-26T09:24:28.723Z",

23    "updatedAt": "2024-08-26T09:24:28.723Z"

24  }

25}

##### Verify Account Number

Please endeavour to verify that the bank account details matches what you intended. Paystack will not be liable for payouts to the wrong bank account.

## [](#initialize-a-split-payment)Initialize a split payment

Split payments can be initialized by using the [Initialize TransactionAPI](https://paystack.com/docs/api/transaction#initialize) endpoint and passing the parameter `subaccount: "ACCT_xxxxxxxxxx"`.

cURLNodePHP

Show Response

1curl https://api.paystack.co/transaction/initialize

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "email": "<EMAIL>", 

5      "amount": "20000", 

6      "subaccount": "ACCT\_xxxxxxxxx" 

7    }'

8\-X POST

1{

2  "status": true,

3  "message": "Authorization URL created",

4  "data": {

5    "authorization\_url": "https://checkout.paystack.com/nkdks46nymizns7",

6    "access\_code": "nkdks46nymizns7",

7    "reference": "nms6uvr1pl"

8  }

9}

Split payments can be used in the following scenario:

-   Shared payment between service provider and platform provider
-   Split profit between different vendors
-   Separate school fees in different account e.g. Tuition, Accomodation, Excursion

## [](#flat-fee)Flat Fee

By default, payments are split by percentage. For example, if a subaccount was created with `percentage_charge: 20`, 20% goes to the main account and the rest goes to the subaccount.

However, you can override this default and specify a flat fee that goes into your main account. To do this, pass the `transaction_charge` key when intializing a transaction.

In the snippet below, the main account gets a flat fee of `10000` and the subaccount gets the rest:

cURLNodePHP

Show Response

1curl https://api.paystack.co/transaction/initialize

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "email": "<EMAIL>", 

5      "amount": "20000",

6      "subaccount": "ACCT\_xxxxxxxxx", 

7      "transaction\_charge": 10000 

8    }'

9\-X POST

1{

2  "status": true,

3  "message": "Authorization URL created",

4  "data": {

5    "authorization\_url": "https://checkout.paystack.com/nkdks46nymizns7",

6    "access\_code": "nkdks46nymizns7",

7    "reference": "nms6uvr1pl"

8  }

9}

## [](#bearer-of-transaction-fee)Bearer of Transaction Fee

By default, the Paystack charges are borne by the main account. To change this to a subaccount, pass the param `bearer: "subaccount"` while intializing a transaction.

cURLNodePHP

Show Response

1curl https://api.paystack.co/transaction/initialize

2\-H "Authorization: Bearer YOUR\_SECRET\_KEY"

3\-H "Content-Type: application/json"

4\-d '{ "email": "<EMAIL>", 

5      "amount": "20000",

6      "subaccount": "ACCT\_xxxxxxxxx", 

7      "bearer": "subaccount" 

8    }'

9\-X POST

1{

2  "status": true,

3  "message": "Authorization URL created",

4  "data": {

5    "authorization\_url": "https://checkout.paystack.com/nkdks46nymizns7",

6    "access\_code": "nkdks46nymizns7",

7    "reference": "nms6uvr1pl"

8  }

9}

##### Invalid Split Configuration

When specifying a bearer, you need to ensure that the amount that goes into the subaccount is sufficient to bear the transaction fee. If the amount is insufficient, you'll get a `400 Bad Request` error.